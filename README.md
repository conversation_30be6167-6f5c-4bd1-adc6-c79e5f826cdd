# 东吴人寿AI智能投保系统

这是一个基于Vue 3和Tailwind CSS开发的AI智能投保系统，集成了Coze实时对话API，提供了沉浸式的投保体验。

## 功能特点

- 精美的登录界面
- 三阶段式布局
  - 初始状态：数字人居中展示
  - 通话状态：左侧数字人，右侧对话
  - 投保状态：左侧数字人，中间产品内容，右侧对话
- 与Coze API集成，支持实时语音对话
- 完整的保险产品展示和投保流程

## 安装与运行

```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 构建生产版本
npm run build
```

## 使用说明

1. 首先在登录页面输入您的Coze API访问令牌(Access Token)和机器人ID(Bot ID)
2. 登录后，点击"立即呼叫"按钮连接到Coze AI
3. 开始与AI助手对话，系统将自动切换到对话布局
4. 对话一段时间后，可以点击"我要投保"按钮进入投保流程
5. 在投保流程中，可以查看产品详情、条款等信息，同时继续与AI对话

## 项目结构

```
src/
  ├── assets/          # 静态资源
  │   └── images/      # 图片资源
  ├── components/      # Vue组件
  │   ├── LoginPage.vue       # 登录页面
  │   ├── DigitalHuman.vue    # 数字人展示
  │   ├── ChatDialog.vue      # 对话界面
  │   ├── InsuranceContent.vue # 保险内容
  │   └── InsuranceLayout.vue # 整体布局
  ├── App.vue          # 主应用组件
  ├── main.js          # 入口文件
  └── index.css        # 全局样式
```

## 注意事项

- 请确保在使用前添加数字人图片到 `src/assets/images/shuziren.png`
- 需要有效的Coze API凭证才能正常使用对话功能
- 本项目需要访问麦克风权限才能进行语音交互

## 技术栈

- Vue 3
- Tailwind CSS
- Coze实时对话API
