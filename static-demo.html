<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>东吴人寿 AI智能投保</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#1C3F94',
            secondary: '#FF5722',
            brand: '#E60012',
            accent: '#FFC107',
            light: '#F2F7FF'
          }
        }
      }
    }
  </script>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
    }
    .gradient-bg {
      background: linear-gradient(90deg, #1C3F94 0%, #2563EB 100%);
    }
    .product-card {
      background: linear-gradient(180deg, #FFFFFF 0%, #F2F7FF 100%);
    }
    .product-banner {
      background: linear-gradient(180deg, #2196F3 0%, #0D47A1 100%);
      position: relative;
      overflow: hidden;
    }
    .product-banner::before {
      content: '';
      position: absolute;
      bottom: -100px;
      left: 0;
      right: 0;
      height: 300px;
      background: radial-gradient(ellipse at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
      border-radius: 50%;
    }
    .call-button {
      background: linear-gradient(90deg, #E60012 0%, #FF5722 100%);
      animation: pulse 2s infinite;
    }
    .tab-active {
      background-color: rgba(255, 255, 255, 0.2);
      border-bottom: 3px solid #FFC107;
    }
    .tab-button {
      transition: all 0.2s ease;
    }
    .tab-button:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    .digital-human-container {
      position: relative;
      overflow: hidden;
      background: linear-gradient(130deg, #4776E6 0%, #1C3F94 100%);
    }
    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(230, 0, 18, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(230, 0, 18, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(230, 0, 18, 0);
      }
    }
    .wave {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 50px;
      background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg"><path fill="white" fill-opacity="0.2" d="M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,160C672,181,768,203,864,186.7C960,171,1056,117,1152,106.7C1248,96,1344,128,1392,144L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
      background-size: cover;
    }
    /* 添加响应式布局和图片溢出控制 */
    .digital-human-img {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      max-width: 100%;
      max-height: 90%;
      object-fit: contain;
    }
    /* 添加光晕效果 */
    .glow-effect {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 70%);
      border-radius: 50%;
      z-index: 0;
    }
  </style>
</head>
<body class="bg-light min-h-screen">
  <!-- 页头 -->
  <header class="w-full bg-white shadow-md">
    <div class="container mx-auto px-4 py-2 flex items-center justify-between">
      <div class="flex items-center">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 100 100" class="text-brand">
            <circle cx="50" cy="50" r="45" fill="#E60012"/>
            <path d="M30,30 Q50,10 70,30 T80,60 Q60,80 40,70 T30,30" fill="#FFF" />
          </svg>
          <span class="ml-2 text-xl font-bold text-brand">东吴人寿</span>
        </div>
        <div class="h-6 w-0.5 bg-gray-300 mx-4"></div>
        <span class="text-xl font-bold text-primary">AI智能投保</span>
      </div>
      <div class="text-sm text-gray-600">
        首页 (产品介绍)
      </div>
    </div>
  </header>

  <!-- 主要内容区 -->
  <main class="container mx-auto px-4 py-4">
    <!-- 顶部通知条 -->
    <div class="w-full gradient-bg text-white p-3 rounded-lg mb-4 flex items-center">
      <span class="text-lg">欢迎您使用东吴人寿AI智能投保系统，屏幕为触摸屏，支持点击、上下滑动操作：</span>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-wrap -mx-2">
      <!-- 左侧：数字人形象区域 -->
      <div class="w-full md:w-1/4 px-2 mb-4">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="digital-human-container h-[500px] relative">
            <div class="glow-effect"></div>
            <!-- 使用外部图片链接，这里是商业形象不确定版权，在实际项目中需要替换为实际的图片地址 -->
            <img src="src/assets/images/shuziren.png" alt="AI数字人" class="digital-human-img">
            <div class="wave"></div>
          </div>
          <div class="p-4 bg-brand text-white flex justify-between items-center">
            <div class="font-bold">您的AI投保助手</div>
            <button class="call-button px-4 py-2 rounded-full text-white font-bold flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              立即呼叫
            </button>
          </div>
        </div>
      </div>

      <!-- 中间：产品展示区域 -->
      <div class="w-full md:w-1/2 px-2 mb-4">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden h-full">
          <!-- 产品导航标签 -->
          <div class="flex text-white gradient-bg overflow-x-auto whitespace-nowrap">
            <div class="px-6 py-3 tab-active font-medium tab-button cursor-pointer">产品介绍</div>
            <div class="px-6 py-3 font-medium tab-button cursor-pointer">产品条款</div>
            <div class="px-6 py-3 font-medium tab-button cursor-pointer">理赔服务</div>
            <div class="px-6 py-3 font-medium tab-button cursor-pointer">保险案例</div>
            <div class="px-6 py-3 font-medium tab-button cursor-pointer">常见问题</div>
          </div>

          <!-- 产品内容区域 -->
          <div class="p-6 product-banner text-white rounded-b-lg h-[470px] flex flex-col">
            <div class="flex justify-center mb-6">
              <div class="bg-white px-8 py-3 rounded-md">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 100 100" class="text-brand mr-2">
                    <circle cx="50" cy="50" r="45" fill="#E60012"/>
                    <path d="M30,30 Q50,10 70,30 T80,60 Q60,80 40,70 T30,30" fill="#FFF" />
                  </svg>
                  <div>
                    <div class="text-primary text-xl font-bold">东吴人寿</div>
                    <div class="text-gray-500 text-xs uppercase">SOOCHOW LIFE</div>
                  </div>
                </div>
              </div>
            </div>

            <h1 class="text-4xl font-bold text-center mb-4 tracking-wider">东吴盛期康宁 <span class="text-accent">(2024版)</span></h1>
            <h2 class="text-3xl font-bold text-center mb-8 tracking-wider">重大疾病保险</h2>
            
            <div class="bg-accent text-primary rounded-full py-3 px-8 text-center max-w-xl mx-auto shadow-lg">
              <p class="text-xl font-bold">经典再续, 保障升级, 守护每一刻安宁!</p>
            </div>

            <div class="mt-auto flex justify-center mb-4">
              <div class="relative">
                <div class="absolute bottom-10 w-40 h-20 bg-accent opacity-40 rounded-full blur-md"></div>
                <svg xmlns="http://www.w3.org/2000/svg" width="180" height="120" viewBox="0 0 180 120">
                  <circle cx="90" cy="60" r="50" fill="#FFC107"/>
                  <path d="M70,30 Q90,10 110,30 T90,80" fill="none" stroke="#FFFFFF" stroke-width="4"/>
                  <circle cx="65" cy="45" r="8" fill="#FFFFFF"/>
                  <circle cx="115" cy="45" r="8" fill="#FFFFFF"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：对话区域 -->
      <div class="w-full md:w-1/4 px-2 mb-4">
        <div class="bg-white rounded-lg shadow-lg h-full p-4 flex flex-col" style="min-height: 556px;">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="font-bold text-gray-700">智能投保助手</div>
          </div>

          <div class="bg-gray-100 rounded-lg p-4 mb-4 flex-grow overflow-auto">
            <p class="text-gray-800">尊敬的用户您好，</p>
            <p class="text-gray-800 mt-2">欢迎您使用东吴人寿AI智能投保系统，此设备仅支持本人投保。您可以点击【立即呼叫】按钮和TA互动，TA会为您详细介绍产品信息，自助为您投保！</p>
            <p class="text-gray-800 mt-2 text-secondary font-medium">温馨提示：</p>
            <p class="text-gray-800">1.屏幕为触摸屏，支持点击、上下滑动操作；</p>
          </div>

          <div class="mt-4">
            <div class="relative">
              <input type="text" class="w-full border border-gray-300 rounded-full py-3 pl-4 pr-12" placeholder="继续对话...">
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</body>
</html> 