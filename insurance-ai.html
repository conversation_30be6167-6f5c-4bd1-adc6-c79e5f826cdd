<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东吴人寿AI智能投保系统</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            height: 100vh;
            overflow: hidden;
            color: #333;
        }
        
        .container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        /* 顶部导航栏 */
        .header {
            background-color: #fff;
            height: 60px;
            width: 100%;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            color: #0056b3;
        }
        
        .logo img {
            height: 40px;
            margin-right: 10px;
        }
        
        .nav-links {
            display: flex;
            margin-left: 40px;
        }
        
        .nav-links a {
            margin: 0 15px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
            color: white;
        }
        
        /* 数字人区域 - 初始状态 */
        .digital-human-container {
            position: absolute;
            width: 100%;
            height: calc(100vh - 60px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease-in-out;
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
            overflow: hidden;
        }
        
        .digital-human {
            height: 70vh;
            position: relative;
            transition: all 0.5s ease-in-out;
            z-index: 10;
        }
        
        .digital-human img {
            height: 100%;
            object-fit: contain;
        }
        
        .call-button {
            margin-top: 20px;
            padding: 12px 30px;
            background-color: #ff4757;
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .call-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 71, 87, 0.6);
        }
        
        /* 对话记录区域 */
        .chat-container {
            position: absolute;
            right: -50%;
            top: 60px;
            width: 50%;
            height: calc(100vh - 60px);
            background-color: white;
            transition: all 0.5s ease-in-out;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            z-index: 20;
        }
        
        .chat-header {
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 15px;
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 18px;
            position: relative;
            clear: both;
        }
        
        .message.ai {
            background-color: #f1f0f0;
            float: left;
            border-bottom-left-radius: 5px;
        }
        
        .message.user {
            background-color: #0084ff;
            color: white;
            float: right;
            border-bottom-right-radius: 5px;
        }
        
        .chat-input {
            padding: 15px;
            border-top: 1px solid #e9ecef;
            display: flex;
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ced4da;
            border-radius: 30px;
            outline: none;
        }
        
        .chat-input button {
            margin-left: 10px;
            padding: 10px 20px;
            background-color: #0084ff;
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
        }
        
        /* 投保按钮 */
        .insurance-button {
            position: absolute;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #ffa502;
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(255, 165, 2, 0.4);
            transition: all 0.3s ease;
            z-index: 30;
            opacity: 0;
            visibility: hidden;
        }
        
        .insurance-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 165, 2, 0.6);
        }
        
        /* 投保内容区域 */
        .insurance-container {
            position: absolute;
            left: 50%;
            top: 60px;
            width: 0;
            height: calc(100vh - 60px);
            background-color: white;
            transition: all 0.5s ease-in-out;
            overflow: hidden;
            z-index: 15;
        }
        
        .insurance-content {
            padding: 20px;
            height: 100%;
            overflow-y: auto;
        }
        
        .insurance-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .insurance-header h2 {
            color: #0056b3;
        }
        
        .insurance-steps {
            display: flex;
            margin-bottom: 30px;
            position: relative;
        }
        
        .insurance-steps::before {
            content: "";
            position: absolute;
            top: 15px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 1;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background-color: #e9ecef;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .step.active .step-number {
            background-color: #0056b3;
            color: white;
        }
        
        .step-label {
            font-size: 14px;
            color: #6c757d;
        }
        
        .step.active .step-label {
            color: #0056b3;
            font-weight: bold;
        }
        
        .insurance-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .form-actions {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
        
        .form-actions button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-prev {
            background-color: #e9ecef;
            color: #495057;
        }
        
        .btn-next {
            background-color: #0056b3;
            color: white;
        }
        
        /* 状态类 */
        .state-talking .digital-human-container {
            width: 50%;
            left: 0;
        }
        
        .state-talking .chat-container {
            right: 0;
        }
        
        .state-talking .insurance-button {
            opacity: 1;
            visibility: visible;
        }
        
        .state-insuring .digital-human-container {
            width: 25%;
            left: 0;
        }
        
        .state-insuring .chat-container {
            width: 25%;
            right: 0;
        }
        
        .state-insuring .insurance-container {
            left: 25%;
            width: 50%;
        }
        
        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            opacity: 0.1;
        }
        
        .bg-circle-1 {
            width: 300px;
            height: 300px;
            background-color: #ff6b6b;
            top: -100px;
            left: -100px;
        }
        
        .bg-circle-2 {
            width: 500px;
            height: 500px;
            background-color: #48dbfb;
            bottom: -200px;
            right: -200px;
        }
        
        .bg-circle-3 {
            width: 200px;
            height: 200px;
            background-color: #feca57;
            top: 50%;
            left: 30%;
        }
        
        /* 动画效果 */
        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }
        
        .digital-human {
            animation: float 6s ease-in-out infinite;
        }
        
        /* 智能助手提示框 */
        .ai-assistant-tooltip {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            max-width: 300px;
            z-index: 100;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.5s ease;
        }
        
        .ai-assistant-tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .ai-assistant-tooltip h4 {
            color: #0056b3;
            margin-bottom: 10px;
        }
        
        .ai-assistant-tooltip p {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .ai-assistant-tooltip .close-tooltip {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container" id="app">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="logo">
                <img src="src/assets/images/shuziren.png" alt="东吴人寿" style="height: 30px;">
                <span>东吴人寿AI智能投保</span>
            </div>
            <div class="nav-links">
                <a href="#" class="active">产品介绍</a>
                <a href="#">产品条款</a>
                <a href="#">理赔服务</a>
                <a href="#">保险案例</a>
                <a href="#">常见问题</a>
            </div>
        </div>
        
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="bg-circle bg-circle-1"></div>
            <div class="bg-circle bg-circle-2"></div>
            <div class="bg-circle bg-circle-3"></div>
        </div>
        
        <!-- 数字人区域 -->
        <div class="digital-human-container">
            <div class="digital-human">
                <img src="src/assets/images/shuziren.png" alt="AI数字人">
            </div>
            <button class="call-button" id="startTalkBtn">立即通话</button>
        </div>
        
        <!-- 对话记录区域 -->
        <div class="chat-container">
            <div class="chat-header">
                与AI助手的对话
            </div>
            <div class="chat-messages" id="chatMessages">
                <!-- 消息将通过JS动态添加 -->
            </div>
            <div class="chat-input">
                <input type="text" placeholder="请输入您的问题..." id="messageInput">
                <button id="sendMessageBtn">发送</button>
            </div>
        </div>
        
        <!-- 投保按钮 -->
        <button class="insurance-button" id="startInsuranceBtn">我要投保</button>
        
        <!-- 投保内容区域 -->
        <div class="insurance-container">
            <div class="insurance-content">
                <div class="insurance-header">
                    <h2>东吴盛朗康宁（2024版）重大疾病保险</h2>
                    <p>经典再续，保障升级，守护每一刻安宁！</p>
                </div>
                
                <div class="insurance-steps">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-label">基本信息</div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-label">健康告知</div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-label">保障选择</div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-label">确认支付</div>
                    </div>
                </div>
                
                <div class="insurance-form">
                    <div class="form-group">
                        <label for="name">姓名</label>
                        <input type="text" id="name" placeholder="请输入您的真实姓名">
                    </div>
                    <div class="form-group">
                        <label for="idCard">身份证号</label>
                        <input type="text" id="idCard" placeholder="请输入您的身份证号码">
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号码</label>
                        <input type="text" id="phone" placeholder="请输入您的手机号码">
                    </div>
                    <div class="form-group">
                        <label for="gender">性别</label>
                        <select id="gender">
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="age">年龄</label>
                        <input type="number" id="age" placeholder="请输入您的年龄">
                    </div>
                    
                    <div class="form-actions">
                        <button class="btn-prev" disabled>上一步</button>
                        <button class="btn-next">下一步</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI助手提示框 -->
        <div class="ai-assistant-tooltip" id="aiTooltip">
            <button class="close-tooltip" id="closeTooltip">×</button>
            <h4>智能投保助手</h4>
            <p>尊敬的用户您好，欢迎使用东吴人寿AI智能投保系统，此设备仅支持本人投保。您可以点击【立即呼叫】按钮和TA互动，TA会为您详细介绍产品信息，自动为您投保！</p>
            <p>温馨提示：<br>1.屏幕为触摸屏，支持点击、上下滑动操作；</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const container = document.getElementById('app');
            const startTalkBtn = document.getElementById('startTalkBtn');
            const startInsuranceBtn = document.getElementById('startInsuranceBtn');
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendMessageBtn = document.getElementById('sendMessageBtn');
            const aiTooltip = document.getElementById('aiTooltip');
            const closeTooltip = document.getElementById('closeTooltip');
            
            // 显示AI助手提示
            setTimeout(() => {
                aiTooltip.classList.add('show');
            }, 1000);
            
            // 关闭提示
            closeTooltip.addEventListener('click', () => {
                aiTooltip.classList.remove('show');
            });
            
            // 开始通话按钮点击事件
            startTalkBtn.addEventListener('click', () => {
                container.classList.add('state-talking');
                setTimeout(() => {
                    addMessage('ai', '您好，我是东吴人寿AI智能投保助手。很高兴为您服务！请问您想了解哪款保险产品？');
                }, 500);
            });
            
            // 开始投保按钮点击事件
            startInsuranceBtn.addEventListener('click', () => {
                container.classList.remove('state-talking');
                container.classList.add('state-insuring');
                addMessage('ai', '好的，我将为您介绍"东吴盛朗康宁（2024版）重大疾病保险"，这是我们的明星产品。请您按照流程填写相关信息。');
            });
            
            // 发送消息按钮点击事件
            sendMessageBtn.addEventListener('click', sendMessage);
            
            // 输入框回车发送
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // 发送消息函数
            function sendMessage() {
                const message = messageInput.value.trim();
                if (message) {
                    addMessage('user', message);
                    messageInput.value = '';
                    
                    // 模拟AI回复
                    setTimeout(() => {
                        if (message.includes('投保') || message.includes('保险')) {
                            addMessage('ai', '东吴盛朗康宁（2024版）重大疾病保险是我们的明星产品，提供全面的重疾保障。您可以点击右下角的"我要投保"按钮开始投保流程。');
                        } else if (message.includes('价格') || message.includes('费用') || message.includes('多少钱')) {
                            addMessage('ai', '保费根据您的年龄、性别和保障额度而定。一般来说，30岁男性选择50万保额，每年保费约为3000元。您可以在投保流程中查看详细报价。');
                        } else if (message.includes('理赔') || message.includes('赔付')) {
                            addMessage('ai', '我们的理赔流程非常简便。发生保险事故后，您可以通过APP、官网或客服电话申请理赔，提交相关材料后，我们会在5个工作日内完成审核。');
                        } else {
                            addMessage('ai', '感谢您的咨询。东吴盛朗康宁（2024版）重大疾病保险提供120种重疾保障，包括恶性肿瘤、急性心肌梗死等。如果您想了解更多，可以直接向我提问。');
                        }
                    }, 1000);
                }
            }
            
            // 添加消息到聊天区域
            function addMessage(type, text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.textContent = text;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        });
    </script>
</body>
</html>
