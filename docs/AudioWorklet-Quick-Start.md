# AudioWorklet VAD增强降噪 - 快速开始

## 5分钟集成指南

### 1. 复制核心文件
将以下文件复制到你的项目中：
- `src/utils/audioProcessor.ts` - 音频处理器管理类
- 参考完整文档中的AudioWorklet处理器代码

### 2. 基础集成代码

```typescript
import { AudioProcessor } from './utils/audioProcessor';

// 初始化
const audioProcessor = new AudioProcessor();
const success = await audioProcessor.initialize();

if (success) {
  // 获取原始音频流
  const originalStream = await navigator.mediaDevices.getUserMedia({
    audio: {
      echoCancellation: false,
      noiseSuppression: false,
      autoGainControl: false
    }
  });

  // 通过AudioWorklet处理
  const processedStream = await audioProcessor.startProcessing(originalStream);

  // 提供给第三方SDK
  if (processedStream) {
    await yourSDK.startAudio({ mediaStream: processedStream });
  } else {
    await yourSDK.startAudio(); // 回退方案
  }

  //腾讯云trtc
  await trtcClient.startLocalAudio({
    microphoneId: '',
    mediaStream: processedStream
  });
}
```

### 3. 添加调试开关

```javascript
// 在你的Vue/React组件中添加
const [audioWorkletEnabled, setAudioWorkletEnabled] = useState(false);

const toggleAudioWorklet = async () => {
  if (audioWorkletEnabled) {
    // 关闭AudioWorklet
    audioProcessor.destroy();
    setAudioWorkletEnabled(false);
  } else {
    // 启用AudioWorklet
    const success = await audioProcessor.initialize();
    setAudioWorkletEnabled(success);
  }
};

// 在调试面板中添加按钮
<button onClick={toggleAudioWorklet}>
  {audioWorkletEnabled ? '关闭' : '开启'}降噪
</button>
```

### 4. 监控VAD状态

```javascript
audioProcessor.setCallbacks({
  onVoiceActivityChanged: (isActive) => {
    console.log('语音活动状态:', isActive);
    // 更新UI状态指示器
    setVoiceActive(isActive);
  },
  onAudioAnalysis: (data) => {
    // 显示调试信息
    setDebugInfo({
      energy: data.energy.toFixed(4),
      speechFrames: data.speechFrames,
      silenceFrames: data.silenceFrames,
      voiceRatio: data.voiceRatio?.toFixed(2)
    });
  }
});
```

## 关键参数调优

### 基础参数（适用于大多数场景）
```javascript
const vadParams = {
  vadThreshold: 0.02,        // 语音检测阈值
  minSpeechFrames: 20,       // 防止误触发
  minSilenceFrames: 60,      // 防止过早结束
  noiseGateThreshold: 0.008, // 噪声门限
  noiseReduction: 0.3        // 降噪强度
};
```

### 嘈杂环境优化
```javascript
const noisyEnvironmentParams = {
  vadThreshold: 0.03,        // 提高阈值
  minSpeechFrames: 25,       // 更保守
  minSilenceFrames: 80,      // 更长的静音判断
  noiseGateThreshold: 0.012, // 更强的噪声门
  noiseReduction: 0.4        // 更强的降噪
};
```

### 安静环境优化
```javascript
const quietEnvironmentParams = {
  vadThreshold: 0.015,       // 降低阈值
  minSpeechFrames: 15,       // 更敏感
  minSilenceFrames: 40,      // 更快响应
  noiseGateThreshold: 0.005, // 更轻的噪声门
  noiseReduction: 0.2        // 更轻的降噪
};
```

## 常见问题快速解决

### 问题1: AudioWorklet不工作
```javascript
// 检查浏览器支持
if (!AudioProcessor.isSupported()) {
  console.error('浏览器不支持AudioWorklet');
  // 使用原始方案
}

// 检查HTTPS
if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
  console.error('AudioWorklet需要HTTPS环境');
}
```

### 问题2: 第三方SDK不支持自定义流
```javascript
try {
  await sdk.startAudio({ mediaStream: processedStream });
} catch (error) {
  console.log('SDK不支持自定义流，使用原始方案');
  await sdk.startAudio();
}
```

### 问题3: 性能问题
```javascript
// 降低更新频率
const vadParams = {
  updateInterval: 20, // 从10增加到20
  // ... 其他参数
};

// 简化计算
calculateEnergy(audioData) {
  // 使用采样而不是全量计算
  const step = 4;
  let sum = 0;
  for (let i = 0; i < audioData.length; i += step) {
    sum += audioData[i] * audioData[i];
  }
  return Math.sqrt(sum / (audioData.length / step));
}
```

## 效果验证

### 1. 对比测试
```javascript
// 添加A/B测试功能
const testWithoutAudioWorklet = async () => {
  await sdk.startAudio(); // 原始方案
};

const testWithAudioWorklet = async () => {
  const processedStream = await audioProcessor.startProcessing(originalStream);
  await sdk.startAudio({ mediaStream: processedStream });
};
```

### 2. 监控指标
```javascript
// 监控关键指标
const metrics = {
  falsePositives: 0,    // 误触发次数
  missedSpeech: 0,      // 漏检次数
  averageLatency: 0,    // 平均延迟
  cpuUsage: 0          // CPU使用率
};

// 在VAD回调中收集数据
onVoiceActivityChanged: (isActive) => {
  const now = performance.now();
  // 记录状态变化时间
  if (isActive) {
    speechStartTime = now;
  } else {
    const duration = now - speechStartTime;
    metrics.averageLatency = (metrics.averageLatency + duration) / 2;
  }
};
```

## 部署检查清单

- [ ] 浏览器支持检查
- [ ] HTTPS环境确认
- [ ] 错误处理和回退方案
- [ ] 性能监控
- [ ] 用户反馈收集
- [ ] 参数调优测试
- [ ] 多设备兼容性测试

## 下一步

1. 根据实际使用场景调优参数
2. 收集用户反馈
3. 监控性能指标
4. 考虑添加更高级的降噪算法

---

*更多详细信息请参考完整文档：AudioWorklet-VAD-Enhancement.md*
