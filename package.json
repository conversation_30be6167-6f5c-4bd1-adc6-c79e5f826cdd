{"name": "tencent-trtc-insurance", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,vue --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.21"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.3", "vite": "^5.3.1"}}