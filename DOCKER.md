# Docker 部署说明

本项目支持使用 Docker 进行开发和部署。以下是相关说明和使用方法。

## 文件说明

- `Dockerfile`: 用于构建生产环境镜像
- `docker-compose.yml`: 用于部署生产环境
- `docker-compose.dev.yml`: 用于运行开发环境
- `nginx.conf`: Nginx 配置文件，用于生产环境
- `.dockerignore`: 指定构建 Docker 镜像时要忽略的文件

## 开发环境

使用以下命令启动开发环境：

```bash
docker-compose -f docker-compose.dev.yml up
```

这将启动一个开发服务器，并监听文件变化。您可以通过访问 http://localhost:5173 来查看应用。

## 生产环境

### 构建和启动

使用以下命令构建并启动生产环境：

```bash
docker-compose up -d --build
```

这将构建应用并在后台启动容器。您可以通过访问 http://localhost:8080 来查看应用。

### 停止服务

使用以下命令停止服务：

```bash
docker-compose down
```

## 自定义配置

### 修改端口

如果需要修改端口映射，请编辑 `docker-compose.yml` 文件中的 `ports` 部分：

```yaml
ports:
  - "自定义端口:80"
```

### 修改 Nginx 配置

如果需要自定义 Nginx 配置，请编辑 `nginx.conf` 文件。

## 注意事项

- 确保您的系统已安装 Docker 和 Docker Compose
- 生产环境使用 Nginx 作为 Web 服务器
- 开发环境直接使用 Vite 的开发服务器
