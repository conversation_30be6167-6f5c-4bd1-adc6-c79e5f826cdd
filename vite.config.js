import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  base: "./",
  server: {
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '3ecec82f.r17.cpolar.top',
      '.cpolar.top', // 允许所有 cpolar.top 子域名
      '.12342234.xyz' // 允许所有 12342234.xyz 子域名
    ],
    host: process.env.VITE_HOST || 'localhost',
    port: 5173,
    strictPort: true,
    watch: {
      usePolling: true
    }
  }
})
