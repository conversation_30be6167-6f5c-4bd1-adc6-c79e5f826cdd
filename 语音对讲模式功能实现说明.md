# 语音对讲模式功能实现说明文档

## 功能概述

本次更新实现了在AI智能投保系统中增加"语音对讲模式"功能，使用户可以在实时通话和语音对讲两种模式之间切换。语音对讲模式下，系统默认关闭麦克风，用户可通过点击悬浮麦克风按钮控制收音，并实时显示AI的聆听状态。

## 主要改动点及代码实现

### 1. 状态变量定义 (App.vue)

```javascript
// 对话方式：realtime-实时通话，intercom-语音对讲
const conversationMode = ref<'realtime' | 'intercom'>('intercom'); 
// 麦克风状态
const microphoneActive = ref(false);
// AI状态：1-聆听中，2-思考中，3-说话中，4-被打断
const aiState = ref<number>(0);
```

### 2. 对话模式切换功能 (App.vue)

```javascript
// 切换对话方式（实时通话/语音对讲）
const switchConversationMode = () => {
  // 如果当前是实时通话，切换到语音对讲
  if (conversationMode.value === 'realtime') {
    conversationMode.value = 'intercom';
    console.log('切换到语音对讲模式');
    
    // 如果正在通话中，停止麦克风
    if (connected.value && trtcClient.value) {
      trtcClient.value.stopLocalAudio();
      microphoneActive.value = false;
    }
  } 
  // 如果当前是语音对讲，切换到实时通话
  else {
    conversationMode.value = 'realtime';
    console.log('切换到实时通话模式');
    
    // 如果正在通话中，启动麦克风
    if (connected.value && trtcClient.value) {
      trtcClient.value.startLocalAudio();
      microphoneActive.value = true;
    }
  }
};
```

### 3. 麦克风控制功能 (App.vue)

```javascript
// 控制麦克风（仅在语音对讲模式下使用）
const toggleMicrophone = async () => {
  if (!connected.value || !trtcClient.value) return;
  
  // 如果是实时通话模式，不做任何操作
  if (conversationMode.value === 'realtime') return;
  
  if (microphoneActive.value) {
    // 关闭麦克风
    await trtcClient.value.stopLocalAudio();
    microphoneActive.value = false;
    console.log('麦克风已关闭');
    // 重置AI状态
    aiState.value = 0;
  } else {
    // 打开麦克风
    await trtcClient.value.startLocalAudio();
    microphoneActive.value = true;
    console.log('麦克风已打开');
    // 设置AI状态为聆听中
    aiState.value = 1;
  }
};
```

### 4. 启动对话时根据模式控制麦克风 (App.vue)

```javascript
// 根据对话方式决定是否启动麦克风
if (conversationMode.value === 'realtime') {
  // 实时通话模式，自动启动麦克风
  await trtcClient.value.startLocalAudio();
  microphoneActive.value = true;
} else {
  // 语音对讲模式，默认不启动麦克风
  microphoneActive.value = false;
}
```

### 5. AI状态回调处理 (App.vue)

```javascript
// 处理AI状态回调消息
else if (data.type === 10001) {
  console.log('[App.vue] 收到AI状态回调:', data);
  const state = data.payload.state;
  aiState.value = state;
  
  // 如果是语音对讲模式，且AI被打断或思考中，自动关闭麦克风
  if (conversationMode.value === 'intercom' && microphoneActive.value && (state === 2 || state === 4)) {
    toggleMicrophone();
  }
}
```

### 6. 悬浮麦克风按钮 (ChatDialog.vue)

```html
<!-- 语音对讲模式下的麦克风按钮 - 固定在右下角 -->
<div 
  v-if="isConnected && conversationMode === 'intercom'"
  class="fixed-mic-button"
>
  <button 
    @click="toggleMicrophone"
    :class="[
      'flex flex-col items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-105',
      microphoneActive ? 'bg-red-500 text-white' : 'bg-primary text-white'
    ]"
  >
    <!-- 麦克风图标 -->
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path v-if="!microphoneActive" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
      <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
    </svg>
    
    <!-- 聆听状态显示 -->
    <div v-if="microphoneActive" class="mt-1 text-xs font-medium whitespace-nowrap">
      <span v-if="aiState === 1" class="animate-pulse">聆听中...</span>
      <span v-else-if="aiState === 2" class="animate-pulse">思考中...</span>
      <span v-else-if="aiState === 3" class="animate-pulse">说话中...</span>
      <span v-else-if="aiState === 4" class="animate-pulse">被打断...</span>
      <span v-else>点击停止</span>
    </div>
    <div v-else class="mt-1 text-xs font-medium">点击开始</div>
  </button>
</div>
```

### 7. 麦克风按钮样式 (ChatDialog.vue)

```css
/* 固定麦克风按钮样式 */
.fixed-mic-button {
  position: absolute;
  bottom: 80px; /* 位于输入框上方 */
  right: 20px;
  z-index: 50;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

/* 麦克风按钮悬停效果 */
.fixed-mic-button button {
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.fixed-mic-button button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* 麦克风按钮激活状态 */
.fixed-mic-button button.bg-red-500 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
```

### 8. 组件间通信 (InsuranceLayout.vue)

```html
<ChatDialog
  :messages="messages"
  :isConnected="layoutMode !== 'initial'"
  :width="layoutMode === 'conversation' ? 'half' : 'quarter'"
  :conversationMode="conversationMode"
  :microphoneActive="microphoneActive"
  :aiState="aiState"
  class="h-full"
  @send-message="emit('send-message', $event)"
  @toggle-microphone="emit('toggle-microphone')"
/>
```

### 9. 调试框中的切换按钮 (App.vue)

```html
<button
  @click="switchConversationMode()"
  class="bg-orange-500 text-white px-2 py-1 rounded text-xs"
>
  切换对话方式
</button>
```

## 使用方法

1. 在调试框中点击"切换对话方式"按钮，可在实时通话和语音对讲模式间切换
2. 语音对讲模式下，点击右下角悬浮的麦克风按钮开始收音
3. 麦克风按钮下方会显示当前AI状态（聆听中/思考中/说话中/被打断）
4. AI回答完毕后，麦克风会自动关闭，需再次点击麦克风按钮继续对话
