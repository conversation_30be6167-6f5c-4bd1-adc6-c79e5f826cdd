<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200 p-4">
    <div class="max-w-md w-full bg-white rounded-xl shadow-lg overflow-hidden">
      <div class="bg-primary py-6 px-8 text-white">
        <div class="flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 100 100" class="mr-2">
            <circle cx="50" cy="50" r="45" fill="#E60012"/>
            <path d="M30,30 Q50,10 70,30 T80,60 Q60,80 40,70 T30,30" fill="#FFF" />
          </svg>
          <div>
            <div class="text-xl font-bold">东吴人寿</div>
            <div class="text-xs uppercase">SOOCHOW LIFE</div>
          </div>
        </div>
        <h1 class="text-2xl font-bold text-center">AI智能投保系统</h1>
      </div>

      <div class="p-8">
        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="accessToken">
            Access Token
          </label>
          <input
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            id="accessToken"
            v-model="accessToken"
            type="text"
            placeholder="请输入您的Access Token"
          />
        </div>

        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="botId">
            Bot ID
          </label>
          <input
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            id="botId"
            v-model="botId"
            type="text"
            placeholder="请输入您的Bot ID"
          />
        </div>

        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="virtualmanKey">
            腾讯数智人项目ID
          </label>
          <input
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            id="virtualmanKey"
            v-model="virtualmanKey"
            type="text"
            placeholder="请输入腾讯数智人项目ID"
          />
        </div>

        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="sign">
            腾讯数智人签名
          </label>
          <input
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
            id="sign"
            v-model="sign"
            type="text"
            placeholder="请输入腾讯数智人签名"
          />
        </div>

        <button
          @click="login"
          class="w-full bg-gradient-to-r from-primary to-blue-500 text-white py-3 px-4 rounded-lg font-medium shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          :disabled="!isFormValid"
        >
          登录系统
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginPage',
  emits: ['login'],
  data() {
    return {
      accessToken: 'cztei_hCnDFLfoHTfo8aR98zhswwR82lnqNMoT1FZR8rem1MCTyUMx',
      botId: '749829947834',
      virtualmanKey: 'e7030e330ec143378cd0437b228b88de', // 腾讯数智人项目ID - 这是一个示例值，需要替换为实际的值
      sign: 'Evi3gkGPwE+KMYZg0Mezjt7LwyPfP+hPd7njPrB7EykbOHDX8eTIoyND0cu3qpQ9MAl1vhCPuLUdMElPgtaIpRitjqxA+kp2lAuA668ziBl3tcKlMSABo/nmAZJFdDE1bhUlynIAXgjpAuVUgjLd8g==' // 腾讯数智人签名 - 这是一个示例值，需要替换为实际的值
    }
  },
  computed: {
    isFormValid() {
      // 只要求填写Coze API的认证信息
      return this.accessToken.trim() !== '' && this.botId.trim() !== '';
      // 如果要求填写腾讯数智人配置，可以取消下面的注释
      // return this.accessToken.trim() !== '' && this.botId.trim() !== '' &&
      //   this.virtualmanKey.trim() !== '' && this.sign.trim() !== '';
    }
  },
  methods: {
    login() {
      if (this.isFormValid) {
        this.$emit('login', {
          accessToken: this.accessToken,
          botId: this.botId,
          virtualmanKey: this.virtualmanKey,
          sign: this.sign
        });
      }
    }
  }
}
</script>