<template>
  <div class="bubble-keywords-container">
    <div
      v-for="(bubble, index) in bubbles"
      :key="index"
      :class="['keyword-bubble', `size-${bubble.size}`, `color-${bubble.color}`]"
      :style="{
        left: `${bubble.position.x}%`,
        top: `${bubble.position.y}%`,
        animationDelay: `${index * 0.5}s`,
        animationDuration: `${5 + (bubble.size === 'small' ? 1 : bubble.size === 'medium' ? 2 : 3)}s`
      }"
      @click="handleBubbleClick(bubble)"
    >
      <span class="bubble-text">{{ bubble.text }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

const props = defineProps({
  // 是否显示气泡
  visible: {
    type: Boolean,
    default: true
  },
  // 自定义热词列表
  keywords: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['bubble-click']);

// 默认热词列表
const defaultKeywords = [
  '建议书生成',
  '我要投保',
  '我要做保全',
  '理赔查询',
  '保单查询',
  '产品推荐',
  '保险咨询',
  '保费计算',
  '退保流程',
  '保险责任',
  '盛朗康宁产品介绍',
  '我今年50岁，我要投保...',
  '明天星期几...'
];

// 合并自定义热词和默认热词
const allKeywords = computed(() => {
  return props.keywords.length > 0 ? props.keywords : defaultKeywords;
});

// 气泡数据
const bubbles = ref([]);

// 生成随机位置
const generateRandomPosition = (side, index) => {
  // 左侧或右侧，稍微分散一点
  const x = side === 'left'
    ? 15 + Math.random() * 20 // 左侧 15-35%
    : 65 + Math.random() * 20; // 右侧 65-85%

  // 随机高度，但根据索引分布在不同区域，避免气泡重叠
  // 将高度区域分为5个区间，但范围更大
  const sectionHeight = 80 / 5; // 总高度范围80%分成5份
  const sectionIndex = index % 5; // 确定在哪个区间
  const minY = 10 + sectionIndex * sectionHeight; // 区间最小值，从10%开始
  const maxY = minY + sectionHeight; // 区间最大值
  const y = minY + Math.random() * (maxY - minY); // 在区间内随机

  return { x, y };
};

// 生成随机大小
const generateRandomSize = () => {
  const sizes = ['small', 'medium', 'large'];
  const randomIndex = Math.floor(Math.random() * sizes.length);
  return sizes[randomIndex];
};

// 生成随机颜色
const generateRandomColor = () => {
  const colors = ['primary', 'secondary', 'accent', 'brand', 'info'];
  const randomIndex = Math.floor(Math.random() * colors.length);
  return colors[randomIndex];
};

// 初始化气泡
const initBubbles = () => {
  const newBubbles = [];

  allKeywords.value.forEach((keyword, index) => {
    // 确定左右侧
    const side = index % 2 === 0 ? 'left' : 'right';

    newBubbles.push({
      text: keyword,
      position: generateRandomPosition(side, index),
      size: generateRandomSize(),
      color: generateRandomColor()
    });
  });

  bubbles.value = newBubbles;
};

// 处理气泡点击
const handleBubbleClick = (bubble) => {
  emit('bubble-click', bubble);
};

onMounted(() => {
  initBubbles();
});
</script>

<style scoped>
.bubble-keywords-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 15;
}

.keyword-bubble {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-weight: 500;
  cursor: pointer;
  pointer-events: auto;
  transform-origin: center;
  animation: float 6s ease-in-out infinite;
  transition: all 0.3s ease;
  text-align: center;
  z-index: 20;

  /* 强制保持圆形 */
  aspect-ratio: 1 / 1;
  width: var(--bubble-size, 70px);
  height: var(--bubble-size, 70px);

  /* 泡泡质感 */
  background: radial-gradient(
    circle at 30% 30%,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.3) 40%,
    rgba(255, 255, 255, 0.1) 80%
  );
  box-shadow:
    inset 0 0 15px rgba(255, 255, 255, 0.5),
    0 5px 15px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(2px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.keyword-bubble::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  filter: blur(2px);
}

.keyword-bubble:hover {
  transform: scale(1.1) translateY(-5px);
  box-shadow:
    inset 0 0 20px rgba(255, 255, 255, 0.7),
    0 8px 20px rgba(0, 0, 0, 0.2);
}

/* 大小变化 */
.size-small {
  font-size: 0.75rem;
  --bubble-size: 60px;
}

.size-medium {
  font-size: 0.85rem;
  --bubble-size: 70px;
}

.size-large {
  font-size: 0.95rem;
  --bubble-size: 80px;
}

/* 颜色变化 - 使用彩色透明泡泡 */
.color-primary {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(71, 118, 230, 0.5) 0%,
    rgba(28, 63, 148, 0.3) 40%,
    rgba(28, 63, 148, 0.1) 80%
  );
  border-color: rgba(28, 63, 148, 0.3);
}

.color-secondary {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(255, 152, 0, 0.5) 0%,
    rgba(255, 87, 34, 0.3) 40%,
    rgba(255, 87, 34, 0.1) 80%
  );
  border-color: rgba(255, 87, 34, 0.3);
}

.color-accent {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(255, 224, 130, 0.5) 0%,
    rgba(255, 193, 7, 0.3) 40%,
    rgba(255, 193, 7, 0.1) 80%
  );
  border-color: rgba(255, 193, 7, 0.3);
}

.color-brand {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(255, 82, 82, 0.5) 0%,
    rgba(230, 0, 18, 0.3) 40%,
    rgba(230, 0, 18, 0.1) 80%
  );
  border-color: rgba(230, 0, 18, 0.3);
}

.color-info {
  background: radial-gradient(
    circle at 30% 30%,
    rgba(128, 222, 234, 0.5) 0%,
    rgba(0, 188, 212, 0.3) 40%,
    rgba(0, 188, 212, 0.1) 80%
  );
  border-color: rgba(0, 188, 212, 0.3);
}

/* 浮动动画 */
@keyframes float {
  0% {
    transform: translateY(0px) scale(1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  25% {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.12);
  }
  50% {
    transform: translateY(0px) scale(1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  75% {
    transform: translateY(8px) scale(0.98);
    box-shadow: 0 5px 9px rgba(0, 0, 0, 0.11);
  }
  100% {
    transform: translateY(0px) scale(1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 气泡文字样式 */
.bubble-text {
  position: relative;
  z-index: 2;
  display: block;
  width: 80%;
  max-height: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  hyphens: auto;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  color: white;

  /* 多行文本截断 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .keyword-bubble {
    width: 50px !important;
    height: 50px !important;
  }

  .bubble-text {
    font-size: 0.7rem;
  }
}
</style>
