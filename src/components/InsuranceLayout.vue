<template>
  <div class="min-h-screen flex flex-col fancy-background">
    <!-- 页头 -->
    <header class="w-full bg-white bg-opacity-80 backdrop-blur-sm shadow-lg flex-shrink-0">
      <div class="container mx-auto px-4 py-3 flex items-center justify-between">
        <div class="flex items-center">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 100 100" class="text-brand">
              <circle cx="50" cy="50" r="45" fill="#E60012"/>
              <path d="M30,30 Q50,10 70,30 T80,60 Q60,80 40,70 T30,30" fill="#FFF" />
            </svg>
            <span class="ml-2 text-xl font-bold text-brand">东吴人寿</span>
          </div>
          <div class="h-6 w-0.5 bg-gray-300 mx-4"></div>
          <span class="text-xl font-bold text-primary">AI智能投保</span>
        </div>
        <div class="text-sm text-gray-600">
          {{ pageTitle }}
        </div>
      </div>
    </header>

    <!-- 主要内容区 -->
    <main class="container mx-auto px-4 py-4 flex-1 flex flex-col overflow-hidden relative">
      <!-- 顶部通知条 -->
      <div class="w-full bg-gradient-to-r from-primary to-blue-600 text-white p-3 rounded-lg mb-4 flex items-center flex-shrink-0 shadow-lg backdrop-blur-sm">
        <span class="text-lg">{{ noticeText }}</span>
        <button
          v-if="showInsuranceButton"
          @click="$emit('start-insurance')"
          class="ml-auto px-4 py-2 bg-accent text-primary font-bold rounded-lg hover:shadow-lg transition-all duration-300"
        >
          我要投保
        </button>
      </div>

      <!-- 统一布局容器 -->
      <div class="flex-1 overflow-hidden relative">
        <!-- 统一布局容器 -->
        <div class="flex w-full h-full absolute inset-0 z-20">
          <!-- 左侧数字人容器 -->
          <div :class="{
            'w-full flex justify-center items-center transition-all duration-500': layoutMode === 'initial',
            'w-1/2 pr-2 transition-all duration-500': layoutMode === 'conversation',
            'w-1/4 pr-2 transition-all duration-500': layoutMode === 'insurance'
          }" class="h-full">
            <div :class="{
              'w-full h-full fixed-height-column enhanced-initial-view': layoutMode === 'initial',
              'w-full h-full fixed-height-column': layoutMode !== 'initial'
            }" :style="{
              '--column-index': 0,
              'height': `${columnHeight}px`
            }">
              <!-- 统一的数字人容器 -->
              <div class="relative h-full">
                <!-- 数字人 - 始终存在，不会重新创建 -->
                <div class="absolute inset-0 z-10 flex justify-center items-center">
                  <TencentDigitalHuman
                    ref="digitalHumanInLayoutRef"
                    :isConnected="layoutMode !== 'initial'"
                    :isConnecting="isConnecting"
                    :layout="layoutMode === 'initial' ? 'full' : (layoutMode === 'conversation' ? 'left' : 'left-third')"
                    class="h-full"
                    :virtualmanKey="virtualmanKey"
                    :sign="sign"
                    @call="emit('call')"
                    @end-call="emit('end-call')"
                    @initialized="handleDigitalHumanInitialized"
                  />
                </div>

                <!-- 初始状态下的装饰元素 -->
                <template v-if="layoutMode === 'initial'">
                  <!-- 热词气泡 -->
                  <BubbleKeywords @bubble-click="handleBubbleClick" />

                  <!-- 左侧宣传海报 -->
                  <div class="absolute left-4 top-4 bottom-4 w-1/4 z-0 rounded-lg overflow-hidden shadow-lg">
                    <div class="h-full bg-gradient-to-br from-blue-500 to-indigo-600 p-4 flex flex-col justify-between">
                      <div>
                        <h3 class="text-white text-xl font-bold mb-2">智能保险顾问</h3>
                        <p class="text-blue-100 text-sm">24小时在线为您提供专业保险咨询服务</p>
                      </div>
                      <div class="mt-4 bg-white bg-opacity-20 p-3 rounded-lg">
                        <p class="text-white text-xs">已为超过<span class="font-bold text-yellow-300">10,000+</span>客户提供服务</p>
                        <p class="text-white text-xs mt-1">客户满意度<span class="font-bold text-yellow-300">98%</span></p>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧宣传海报 -->
                  <div class="absolute right-4 top-4 bottom-4 w-1/4 z-0 rounded-lg overflow-hidden shadow-lg">
                    <div class="h-full bg-gradient-to-br from-purple-500 to-pink-600 p-4 flex flex-col justify-between">
                      <div>
                        <h3 class="text-white text-xl font-bold mb-2">保险产品推荐</h3>
                        <p class="text-purple-100 text-sm">根据您的需求智能推荐最适合的保险产品</p>
                      </div>
                      <div class="mt-4 bg-white bg-opacity-20 p-3 rounded-lg">
                        <p class="text-white text-xs">覆盖<span class="font-bold text-yellow-300">50+</span>种保险产品</p>
                        <p class="text-white text-xs mt-1">专业定制<span class="font-bold text-yellow-300">个性化方案</span></p>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- 中间产品内容 -->
          <div class="h-full transition-all duration-500" :class="{
            'w-0 px-0 overflow-hidden': layoutMode === 'initial' || layoutMode === 'conversation',
            'w-1/2 px-2': layoutMode === 'insurance'
          }">
            <transition name="expand-center">
              <div v-if="layoutMode === 'insurance'" class="h-full fixed-height-column" :style="{'--column-index': 1, 'height': `${columnHeight}px`}">
                <InsuranceContent class="h-full" />
              </div>
            </transition>
          </div>

          <!-- 右侧对话框容器 -->
          <div :class="{
            'w-0 overflow-hidden transition-all duration-500': layoutMode === 'initial',
            'w-1/2 pl-2 transition-all duration-500': layoutMode === 'conversation',
            'w-1/4 pl-2 transition-all duration-500': layoutMode === 'insurance'
          }" class="h-full">
            <div class="h-full fixed-height-column" :style="{'--column-index': 2, 'height': `${columnHeight}px`}">
              <ChatDialog
                :messages="messages"
                :isConnected="layoutMode !== 'initial'"
                :width="layoutMode === 'conversation' ? 'half' : 'quarter'"
                :conversationMode="conversationMode"
                :microphoneActive="microphoneActive"
                :aiState="aiState"
                class="h-full"
                @send-message="emit('send-message', $event)"
                @toggle-microphone="emit('toggle-microphone')"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 调试信息 - 保持原样 -->
      <div v-if="false" class="mt-2 p-3 bg-white bg-opacity-70 backdrop-blur-sm rounded-lg text-xs text-gray-600 flex-shrink-0 shadow-sm border border-gray-200">
        <strong>调试信息:</strong> 模式: <span class="text-primary font-medium">{{ props.layoutMode }}</span>,
        消息: <span class="text-primary font-medium">{{ props.messages.length }}</span> 条,
        显示投保按钮: <span class="text-primary font-medium">{{ props.showInsuranceButton ? '是' : '否' }}</span>
      </div>
    </main>

    <!-- 页脚 -->
    <!-- <footer class="bg-white bg-opacity-80 backdrop-blur-sm py-3 border-t border-gray-200 flex-shrink-0 shadow-md">
      <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
        <p>© 2024 东吴人寿保险股份有限公司 版权所有</p>
        <p class="mt-1">ICP备案号：苏ICP备XXXXXXXX号-X</p>
      </div>
    </footer> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import TencentDigitalHuman from './TencentDigitalHuman.vue';
import ChatDialog from './ChatDialog.vue';
import InsuranceContent from './InsuranceContent.vue';
import BubbleKeywords from './BubbleKeywords.vue';

const props = defineProps({
  layoutMode: {
    type: String,
    default: 'initial', // 'initial', 'conversation', 'insurance'
    validator(value) {
      return ['initial', 'conversation', 'insurance'].includes(value);
    }
  },
  messages: {
    type: Array,
    default: () => []
  },
  showInsuranceButton: {
    type: Boolean,
    default: false
  },
  isConnecting: {
    type: Boolean,
    default: false
  },
  virtualmanKey: {
    type: String,
    default: ''
  },
  sign: {
    type: String,
    default: ''
  },
  // 新增属性：对话方式
  conversationMode: {
    type: String,
    default: 'intercom', // 'realtime' or 'intercom'
    validator(value) {
      return ['realtime', 'intercom'].includes(value);
    }
  },
  // 新增属性：麦克风状态
  microphoneActive: {
    type: Boolean,
    default: false
  },
  // 新增属性：AI状态
  aiState: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['call', 'send-message', 'start-insurance', 'end-call', 'toggle-microphone']);

const debug = ref(true); // 调试模式
const columnHeight = ref(600); // 默认列高度
const minColumnHeight = ref(400); // 最小列高度
const maxColumnHeight = ref(700); // 最大列高度
// const isConnected = ref(false); // 新增连接状态 - this was in data but seems unused or covered by props.isConnecting or layoutMode
// const digitalHumanLayout = ref('full'); // 新增数字人布局 - this was in data but seems unused or determined by layoutMode

// 处理热词气泡点击
const handleBubbleClick = (bubble) => {
  console.log('热词气泡被点击:', bubble);
  // 触发开始通话事件，与"立即通话"按钮行为一致
  emit('call');
};

// 腾讯数智人配置 - 从props中获取
const virtualmanKey = computed(() => props.virtualmanKey);
const sign = computed(() => props.sign);

const digitalHumanInLayoutRef = ref(null);

// 处理数字人初始化完成事件
const handleDigitalHumanInitialized = (data) => {
  console.log('数字人初始化完成:', data);
};

// 已移除setMouthOpenRelay方法，因为不再使用Live2D

// 获取数字人实例
const getDigitalHumanInstance = () => {
  if (digitalHumanInLayoutRef.value) {
    console.log('[InsuranceLayout] 返回数字人实例');
    return digitalHumanInLayoutRef.value;
  } else {
    console.warn('[InsuranceLayout] 数字人实例不可用，尝试重新获取');
    // 尝试通过DOM查找数字人实例
    const digitalHumanElement = document.querySelector('.digital-human-container');
    if (digitalHumanElement && digitalHumanElement.__vueParentComponent?.ctx) {
      console.log('[InsuranceLayout] 通过DOM找到数字人实例');
      return digitalHumanElement.__vueParentComponent.ctx;
    }
    console.warn('[InsuranceLayout] 无法获取数字人实例');
    return null;
  }
};

defineExpose({
  getDigitalHumanInstance
});

const pageTitle = computed(() => {
  const titles = {
    'initial': '首页',
    'conversation': '智能助手咨询',
    'insurance': '智能投保流程'
  };
  return titles[props.layoutMode] || '首页';
});

const noticeText = computed(() => {
  const notices = {
    'initial': '欢迎使用东吴人寿AI智能投保系统，点击"立即呼叫"开始对话',
    'conversation': '您正在与AI智能助手对话，您可以向AI智能助手咨询保险相关问题',
    'insurance': '您正在进行智能投保，您可以在屏幕滑动查阅相关产品资料与操作'
  };
  return notices[props.layoutMode] || '欢迎使用东吴人寿AI智能投保系统';
});

watch(() => props.messages, (newVal) => {
  console.log('InsuranceLayout: 消息列表已更新，长度:', newVal?.length);
}, { deep: true, immediate: true });

watch(() => props.layoutMode, (newVal, oldVal) => {
  console.log('InsuranceLayout: 布局模式已更新:', newVal, '从', oldVal);
  nextTick(() => {
    adjustContainerHeight();
  });
});

const adjustContainerHeight = () => {
  const viewportHeight = window.innerHeight;
  const mainElement = document.querySelector('main');
  if (mainElement) {
    const headerHeight = document.querySelector('header')?.offsetHeight || 0;
    const footerHeight = document.querySelector('footer')?.offsetHeight || 0; // Assuming footer might be un-commented
    const noticeElement = mainElement.querySelector('div:first-child');
    const noticeHeight = noticeElement?.offsetHeight || 0;

    let calculatedDebugHeight = 0;
    if (debug.value) {
        const debugElement = mainElement.querySelector('div:last-child');
        if (debugElement && debugElement.classList.contains('text-xs')) { // A bit fragile, but to identify the debug div
            calculatedDebugHeight = debugElement.offsetHeight || 0;
        }
    }

    const contentHeight = viewportHeight - headerHeight - footerHeight - noticeHeight - calculatedDebugHeight - 32; // 32px for padding

    mainElement.style.minHeight = `${contentHeight}px`;

    columnHeight.value = Math.max(
      minColumnHeight.value,
      Math.min(maxColumnHeight.value, contentHeight - 20)
    );

    console.log('调整列高度为:', columnHeight.value, 'px');
  }
};

onMounted(() => {
  console.log('InsuranceLayout 组件已挂载，当前模式:', props.layoutMode);
  console.log('InsuranceLayout 初始消息:', props.messages);
  adjustContainerHeight();
  window.addEventListener('resize', adjustContainerHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustContainerHeight);
});

</script>

<style scoped>
/* 添加必要的样式覆盖 */
.container {
  height: 100%;
}

/* 自适应高度的列，内容溢出时滚动 */
.fixed-height-column {
  /* 高度现在通过JS动态设置 */
  min-height: 400px;
  overflow-y: auto; /* 内容溢出时显示滚动条 */
  backdrop-filter: blur(5px);
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.fixed-height-column:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 绝丽的背景设计 */
.fancy-background {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;
}

.fancy-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="%231C3F94" stroke-width="0.5" stroke-opacity="0.1"/></svg>');
  opacity: 0.3;
  z-index: 0;
}

.fancy-background::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 90%;
  height: 90%;
  background: radial-gradient(circle, rgba(28, 63, 148, 0.1) 0%, rgba(28, 63, 148, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

/* 确保内容在背景上层 */
main, header, footer {
  position: relative;
  z-index: 1;
}

/* 添加动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 浮动元素动画 */
.fixed-height-column {
  animation: float 6s ease-in-out infinite;
  animation-delay: calc(var(--column-index, 0) * 1s);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 左侧收缩动画 */
.shrink-left-enter-active,
.shrink-left-leave-active {
  transition: all 0.3s ease-in-out;
  transform-origin: left center;
}

.shrink-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.shrink-left-leave-to {
  transform: translateX(-50%);
  opacity: 0;
}

/* 右侧滑入滑出动画 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease-in-out;
  transform-origin: right center;
}

.slide-right-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(50%);
  opacity: 0;
}

/* 中间展开动画 */
.expand-center-enter-active,
.expand-center-leave-active {
  transition: all 0.3s ease-in-out;
  transform-origin: center center;
}

.expand-center-enter-from {
  transform: scaleX(0.5);
  opacity: 0;
}

.expand-center-leave-to {
  transform: scaleX(0.5);
  opacity: 0;
}

/* 确保布局切换时的平滑过渡 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

/* 两栏和三栏模式的容器过渡 */
.flex.w-full.h-full.absolute {
  transition: opacity 0.3s ease;
}

/* 增强初始视图的样式 */
.enhanced-initial-view {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 255, 0.9) 100%);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.enhanced-initial-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="%231C3F94" stroke-width="0.5" stroke-opacity="0.05"/></svg>');
  opacity: 0.5;
  z-index: 0;
}

/* 左右海报的动画效果 */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.enhanced-initial-view .absolute.left-4 {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.enhanced-initial-view .absolute.right-4 {
  animation: fadeInRight 0.8s ease-out forwards;
}

/* 媒体查询 - 小屏幕调整 */
@media (max-height: 768px) {
  .fancy-background {
    padding-bottom: 10px;
  }

  .fixed-height-column {
    min-height: 350px;
  }

  .fixed-height-column:hover {
    transform: translateY(-1px);
  }

  /* 减小动画幅度 */
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0px); }
  }
}

@media (max-height: 600px) {
  /* 对非常小的屏幕禁用浮动动画 */
  .fixed-height-column {
    animation: none;
  }
}
</style>