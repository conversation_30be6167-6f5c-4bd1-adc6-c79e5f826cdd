<template>
  <div :class="[
    'digital-human-container transition-all duration-500 ease-in-out overflow-hidden rounded-lg h-full',
    layout === 'full' ? 'w-full' :
    layout === 'left' ? 'w-full' :
    layout === 'left-third' ? 'w-full' : ''
  ]">
    <div class="glow-effect absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white bg-opacity-30 rounded-full filter blur-xl z-0"></div>

    <div class="relative h-full flex flex-col justify-center items-center">
      <div class="flex-grow flex justify-center items-center w-full h-full overflow-hidden">
        <!-- 数字人视频播放器 -->
        <div class="video-container max-w-3xl mx-auto w-full h-full">
          <!-- 腾讯数智人IVH容器 -->
          <div
            ref="videoAreaRef"
            class="video-area w-full h-full relative z-10"
            :style="{ display: showTRTCVideo ? 'none' : 'flex' }"
          ></div>

          <!-- TRTC远端视频容器 -->
          <div
            :id="trtcVideoContainerId"
            class="video-area w-full h-full relative z-10"
            :style="{ display: showTRTCVideo ? 'flex' : 'none' }"
          ></div>
        </div>

        <!-- 加载界面 -->
        <div v-show="isLoading" class="loading absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-50 z-20">
          <div class="text-white text-center">
            <div class="mb-2">数智人加载中</div>
            <div class="loading-dots">...</div>
          </div>
        </div>
      </div>

      <div class="absolute bottom-4 left-0 right-0 flex justify-center z-20">
        <button
          v-if="!isConnected && !isConnecting"
          @click="$emit('call')"
          class="call-button px-6 py-3 bg-gradient-to-r from-primary to-primary-dark text-white font-bold rounded-full shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl flex items-center justify-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
          立即通话
        </button>

        <div v-else-if="isConnecting" class="px-4 py-2 bg-primary bg-opacity-80 text-white rounded-lg text-center connecting-animation">
          <span class="connecting-dots">正在连接</span>
        </div>

        <div v-else class="flex items-center space-x-2">
          <div class="px-4 py-2 bg-primary bg-opacity-80 text-white rounded-lg text-center">
            <span class="animate-pulse" style="color: green;">● </span>通话中...
          </div>
          <button
            @click="handleEndCall"
            class="end-call-button px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white font-bold rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V4a1 1 0 00-1-1H3zm14 12H3V5h14v10z" />
              <path d="M9 7h2v6H9V7zm4 0h2v6h-2V7zM5 7h2v6H5V7z" />
            </svg>
            结束
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
  isConnected: {
    type: Boolean,
    default: false
  },
  isConnecting: {
    type: Boolean,
    default: false
  },
  layout: {
    type: String,
    default: 'full', // 'full', 'left', 'left-third'
    validator(value) {
      return ['full', 'left', 'left-third'].includes(value);
    }
  },
  // 腾讯数智人配置
  virtualmanKey: {
    type: String,
    default: ''
  },
  sign: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['call', 'end-call', 'initialized']);

const videoAreaRef = ref(null);
const isLoading = ref(true);
const isInitialized = ref(false);
const sessionId = ref('');

// TRTC视频相关状态
const trtcVideoContainerId = ref('trtc-video-container');
const showTRTCVideo = ref(false);

// 初始化数智人
const initDigitalHuman = async () => {
  if (!videoAreaRef.value) return;

  try {
    // 检查全局IVH对象是否存在
    if (!window.IVH) {
      console.error('腾讯数智人SDK未加载');
      return;
    }

    console.log('开始初始化数智人，参数:', {
      sign: props.sign,
      virtualmanProjectId: props.virtualmanKey,
      element: videoAreaRef.value
    });

    // 确保之前的会话已关闭
    try {
      if (window.IVH && typeof window.IVH.closeSession === 'function') {
        await window.IVH.closeSession();
      }
    } catch (e) {
      console.warn('关闭之前的会话失败，可能不存在:', e);
    }

    // 禁用全局报告功能，避免不必要的错误
    if (window.Global && typeof window.Global.isCanReport !== 'undefined') {
      window.Global.isCanReport = false;
    }

    // 初始化SDK
    window.IVH.init({
      sign: props.sign,
      virtualmanProjectId: props.virtualmanKey,
      element: videoAreaRef.value,
      mute: true, // 设置静音
      volume: 0   // 设置音量为0
    });

    // 设置事件监听
    window.IVH.on("canplay", async () => {
      console.log('数智人可以播放了 - canplay事件触发');

      // 立即设置初始化状态，这样其他地方可以知道数智人已经准备好了
      isInitialized.value = true;

      // 确保视频区域可见
      if (videoAreaRef.value) {
        const videoElements = videoAreaRef.value.querySelectorAll('video');
        console.log('找到视频元素数量:', videoElements.length);

        videoElements.forEach(video => {
          // 设置视频元素样式
          video.style.width = '100%';
          video.style.height = '100%';
          video.style.objectFit = 'contain';

          // 确保视频元素静音
          video.muted = true;
          video.volume = 0;

          console.log('设置视频元素样式和静音:', video);
        });
      }

      // 强制隐藏加载遮罩
      isLoading.value = false;
      console.log('已设置isLoading为false');

      // 通知父组件初始化完成
      emit('initialized', { sessionId: sessionId.value });
    });

    window.IVH.on("socket", (data) => {
      console.log('数智人socket消息:', data);
    });

    window.IVH.on("error", (error) => {
      console.error('数智人错误:', error);
    });

    // 建立会话
    console.log('开始创建数智人会话');
    const result = await window.IVH.createSession({
      userId: generateUserId()
    });

    if (result) {
      sessionId.value = result.sessionId;
      console.log('数智人会话创建成功:', result);
    } else {
      console.error('数智人会话创建失败，未返回结果');
    }
  } catch (error) {
    console.error('初始化数智人失败:', error);
  }
};

// 生成用户ID
const generateUserId = () => {
  const storageKey = 'apaas_stream_userId';
  let userId = localStorage.getItem(storageKey);

  if (!userId) {
    userId = window.uuidv4 ? window.uuidv4() : Math.random().toString(36).substring(2, 15);
    localStorage.setItem(storageKey, userId);
  }

  return userId;
};

// 开始会话
const startSession = async () => {
  try {
    console.log('开始数智人会话...');

    // 检查会话是否存在，如果不存在则重新创建
    if (!sessionId.value) {
      console.log('会话ID不存在，尝试重新创建会话');
      try {
        const createResult = await window.IVH.createSession({
          userId: generateUserId()
        });

        if (createResult) {
          sessionId.value = createResult.sessionId;
          console.log('重新创建会话成功:', createResult);
        } else {
          console.error('重新创建会话失败，未返回结果');
          return false;
        }
      } catch (createError) {
        console.error('重新创建会话失败:', createError);
        return false;
      }
    }

    // 开始会话
    let result;
    try {
      result = await window.IVH.startSession();
    } catch (startError) {
      // 如果开始会话失败，可能是会话不存在，尝试重新创建会话
      if (startError && (
          startError.message?.includes('session不存在') ||
          startError.code === 110018 ||
          startError.message?.includes('SessionNotExist')
        )) {
        console.log('会话不存在，尝试重新创建会话');
        try {
          const createResult = await window.IVH.createSession({
            userId: generateUserId()
          });

          if (createResult) {
            sessionId.value = createResult.sessionId;
            console.log('重新创建会话成功:', createResult);
            // 再次尝试开始会话
            result = await window.IVH.startSession();
          } else {
            console.error('重新创建会话失败，未返回结果');
            return false;
          }
        } catch (recreateError) {
          console.error('重新创建会话失败:', recreateError);
          return false;
        }
      } else {
        // 其他错误，直接抛出
        throw startError;
      }
    }

    if (result) {
      console.log('数智人会话开始成功');

      // 确保视频区域可见
      if (videoAreaRef.value) {
        videoAreaRef.value.style.display = 'flex';

        // 查找视频元素并确保其样式正确
        const videoElements = videoAreaRef.value.querySelectorAll('video');
        if (videoElements.length > 0) {
          videoElements.forEach(video => {
            // 设置视频元素样式
            video.style.width = '100%';
            video.style.height = '100%';
            video.style.objectFit = 'cover'; // 使用cover而不是contain

            // 确保视频元素静音
            video.muted = true;
            video.volume = 0;

            console.log('会话开始时设置视频元素样式和静音:', video);
          });
        } else {
          console.warn('未找到视频元素');
        }
      }

      // 强制隐藏加载遮罩
      isLoading.value = false;
      console.log('会话开始成功，强制隐藏加载遮罩');

      return true;
    }
    return false;
  } catch (error) {
    console.error('开始数智人会话失败:', error);
    return false;
  }
};

// 发送文本
const sendText = async (text, textDrive = false) => {
  try {
    await window.IVH.play({
      command: "text",
      data: text,
      chatCommand: textDrive ? "NotUseChat" : ""
    });
    return true;
  } catch (error) {
    console.error('发送文本失败:', error);
    return false;
  }
};

// 使用流式文本驱动数字人嘴型，但不发声
const sendStreamText = async (text, seq = 1, reqId = '', isFinal = false) => {
  try {
    console.log('发送流式文本到数字人:', text, '序列号:', seq, 'reqId:', reqId, 'isFinal:', isFinal);

    // 确保数字人静音
    if (videoAreaRef.value) {
      const videoElements = videoAreaRef.value.querySelectorAll('video');
      videoElements.forEach(video => {
        // 设置视频元素静音
        video.muted = true;
        video.volume = 0;
        console.log('设置数字人静音');
      });
    }

    // 检查会话是否存在，如果不存在则重新创建并开始会话
    if (!sessionId.value) {
      console.log('会话ID不存在，尝试重新创建并开始会话');
      const sessionStarted = await startSession();
      if (!sessionStarted) {
        console.error('无法创建或开始会话，无法发送流式文本');
        return false;
      }
    }

    // 如果没有提供reqId，则生成一个新的
    if (!reqId) {
      reqId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
      console.log('未提供reqId，生成新的请求ID:', reqId);
    }

    try {
      // 使用stream命令发送文本，驱动嘴型但不发声
      await window.IVH.play({
        command: "stream",
        data: text,
        seq: seq,
        isFinal: isFinal, // 使用传入的isFinal参数
        smartActionEnabled: true,
        isSentence: false,
        isInsertSentence: false,
        mute: true, // 添加静音参数
        reqId: reqId // 使用请求ID
      });
    } catch (streamError) {
      // 如果发送失败，可能是会话不存在
      if (streamError && (
          streamError.message?.includes('session不存在') ||
          streamError.code === 110018 ||
          streamError.message?.includes('SessionNotExist')
        )) {
        console.log('发送流式文本时会话不存在，尝试重新创建并开始会话');
        const sessionStarted = await startSession();
        if (!sessionStarted) {
          throw new Error('无法创建或开始会话，无法继续发送流式文本');
        }

        // 重新发送文本
        await window.IVH.play({
          command: "stream",
          data: text,
          seq: seq,
          isFinal: isFinal, // 使用传入的isFinal参数
          smartActionEnabled: true,
          isSentence: false,
          isInsertSentence: false,
          mute: true,
          reqId: reqId
        });
      } else {
        // 其他错误，直接抛出
        throw streamError;
      }
    }

    return true;
  } catch (error) {
    console.error('发送流式文本失败:', error);
    return false;
  }
};



// 停止当前播放
const stopPlaying = async () => {
  try {
    window.IVH.stop();
    return true;
  } catch (error) {
    console.error('停止播放失败:', error);
    return false;
  }
};

// 停止会话
const stopSession = async () => {
  try {
    if (window.IVH) {
      window.IVH.stop();
      await window.IVH.closeSession();
      isInitialized.value = false;
      sessionId.value = '';
    }
  } catch (error) {
    console.error('停止数智人会话失败:', error);
  }
};

// 设置TRTC视频容器ID
const setTRTCVideoContainer = (videoContainerId) => {
  console.log('[TencentDigitalHuman] 设置TRTC视频容器ID:', videoContainerId);
  trtcVideoContainerId.value = videoContainerId;

  // 隐藏加载状态，因为TRTC视频流将接管显示
  isLoading.value = false;

  console.log('[TencentDigitalHuman] TRTC视频容器已设置');
};

// 处理结束通话按钮点击
const handleEndCall = () => {
  console.log('结束通话按钮被点击');

  // 停止当前播放
  stopPlaying();

  // 发送end-call事件给父组件
  emit('end-call');
};

onMounted(async () => {
  console.log('TencentDigitalHuman组件已挂载');
  // 如果有配置参数，自动初始化并开始服务
  if (props.virtualmanKey && props.sign) {
    console.log('检测到数智人配置，自动初始化并开始服务');

    // 设置一个超时，确保加载遮罩不会一直显示
    const loadingTimeout = setTimeout(() => {
      if (isLoading.value) {
        console.log('加载超时，强制隐藏加载遮罩');
        isLoading.value = false;
        isInitialized.value = true;
      }
    }, 10000); // 10秒后强制隐藏加载遮罩

    try {
      // 初始化数字人
      await initDigitalHuman();

      // 监听canplay事件，在数字人准备好后自动开始会话
      const canplayPromise = new Promise((resolve) => {
        if (isInitialized.value) {
          resolve();
        } else {
          const canplayHandler = () => {
            console.log('数智人可以播放了，准备开始会话');
            resolve();
          };
          window.IVH.on('canplay', canplayHandler);

          // 5秒后如果还没有初始化完成，也继续执行
          setTimeout(() => {
            if (!isInitialized.value) {
              console.log('5秒后仍未初始化完成，但继续执行');
              isInitialized.value = true;
              isLoading.value = false;
              resolve();
            }
          }, 5000);
        }
      });

      // 等待canplay事件或超时
      await canplayPromise;

      // 自动开始会话
      console.log('数智人初始化完成，自动开始会话');
      await startSession();

      // 通知父组件数字人已初始化并开始服务
      emit('initialized', { sessionId: sessionId.value });

    } catch (error) {
      console.error('初始化数智人或开始会话失败:', error);
      isLoading.value = false;
    }
  }
});

onBeforeUnmount(() => {
  console.log('TencentDigitalHuman组件即将卸载，停止会话');
  stopSession();
});

// 暴露方法给父组件
defineExpose({
  startSession,
  sendStreamText,
  stopPlaying,
  stopSession,
  setTRTCVideoContainer
});
</script>

<style scoped>
.digital-human-container {
  background: rgb(214, 218, 224); /* 简单调整为纯色背景 */
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* 视频容器 - 限制最大宽度，模拟开发者工具打开时的效果 */
.video-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 80%; /* 限制最大宽度 */
  margin: 0 auto;
}

.video-area {
  width: 100%;
  height: 100%;
  background-color: transparent;
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 确保视频元素正确显示并完整展示数字人 */
.video-area video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important; /* 使用cover确保填充整个区域 */
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
}

.loading {
  color: white;
  font-size: 18px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.loading-dots {
  font-size: 24px;
  animation: loadingDotsAnimation 1.5s infinite;
  display: inline-block;
  min-width: 30px;
  text-align: left;
}

@keyframes loadingDotsAnimation {
  0% { content: '.'; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: '.'; }
}

.call-button {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 0, 18, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 0, 18, 0);
  }
}

.connecting-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}
</style>
