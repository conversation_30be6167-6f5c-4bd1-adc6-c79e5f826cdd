<template>
  <div :class="[
    'bg-white rounded-lg shadow-lg overflow-hidden flex flex-col transition-all duration-500 ease-in-out h-full',
    width === 'half' ? 'w-full' : 'w-full',
  ]">
    <div class="bg-gray-100 p-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center">
        <div class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <div class="font-bold text-gray-800">AI智能投保助手</div>
          <div class="text-xs text-gray-500">
            <span v-if="isConnected" class="text-green-500 flex items-center">
              <span class="inline-block w-2 h-2 rounded-full bg-green-500 mr-1 animate-pulse"></span>
              在线
            </span>
            <span v-else class="text-gray-500">离线</span>
          </div>
        </div>
      </div>
    </div>

    <div class="flex-1 p-4 overflow-y-auto" ref="messagesContainer">
      <div v-if="!messages || messages.length === 0" class="text-center text-gray-400 mt-10">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        <p>还没有对话记录</p>
        <p class="text-sm mt-1">点击"立即呼叫"开始对话</p>
      </div>

      <div v-else>
        <div class="text-xs text-gray-400 mb-2 text-center">共 {{ messages.length }} 条消息</div>
        <div
          v-for="(message, index) in messages"
          :key="message.id || index"
          :class="[
            'max-w-[80%] rounded-lg p-3 mb-4 break-words',
            message.type === 'ai'
              ? 'bg-gray-100 text-gray-800 rounded-br-none mr-auto'
              : 'bg-primary text-white rounded-bl-none ml-auto'
          ]"
        >
          {{ message.content }}
        </div>
      </div>
    </div>

    <!-- 语音对讲模式下的麦克风按钮 - 固定在右下角 -->
    <div
      v-if="isConnected && conversationMode === 'intercom'"
      class="fixed-mic-button"
    >
      <button
        @click="toggleMicrophone"
        :class="[
          'flex flex-col items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-105',
          microphoneActive ? 'bg-red-500 text-white' : 'bg-primary text-white'
        ]"
      >
        <!-- 麦克风图标 -->
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path v-if="!microphoneActive" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
        </svg>

        <!-- 聆听状态显示 -->
        <div v-if="microphoneActive" class="mt-1 text-xs font-medium whitespace-nowrap">
          <span v-if="aiState === 1" class="animate-pulse">聆听中...</span>
          <span v-else-if="aiState === 2" class="animate-pulse">思考中...</span>
          <span v-else-if="aiState === 3" class="animate-pulse">说话中...</span>
          <span v-else-if="aiState === 4" class="animate-pulse">被打断...</span>
          <span v-else>点击停止</span>
        </div>
        <div v-else class="mt-1 text-xs font-medium">点击开始</div>
      </button>
    </div>

    <div class="border-t border-gray-200 p-4 flex-shrink-0">
      <div class="relative">
        <input
          type="text"
          v-model="inputMessage"
          @keyup.enter="sendMessage"
          placeholder="请输入您的问题..."
          class="w-full border border-gray-300 rounded-full py-2 pl-4 pr-12 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          :disabled="!isConnected"
        />
        <button
          @click="sendMessage"
          :disabled="!isConnected || !inputMessage.trim()"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-primary disabled:text-gray-400"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatDialog',
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    isConnected: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: 'quarter', // 'half' or 'quarter'
      validator(value) {
        return ['half', 'quarter'].includes(value);
      }
    },
    // 新增属性：对话方式
    conversationMode: {
      type: String,
      default: 'intercom', // 'realtime' or 'intercom'
      validator(value) {
        return ['realtime', 'intercom'].includes(value);
      }
    },
    // 新增属性：麦克风状态
    microphoneActive: {
      type: Boolean,
      default: false
    },
    // 新增属性：AI状态
    aiState: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      inputMessage: ''
    }
  },
  methods: {
    sendMessage() {
      if (this.inputMessage.trim() && this.isConnected) {
        this.$emit('send-message', this.inputMessage);
        this.inputMessage = '';
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
        }
      });
    },
    // 新增方法：切换麦克风
    toggleMicrophone() {
      this.$emit('toggle-microphone');
    }
  },
  watch: {
    messages: {
      handler() {
        console.log('消息列表已更新:', this.messages);
        this.scrollToBottom();
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    console.log('ChatDialog 组件已挂载, 消息:', this.messages);
    this.scrollToBottom();

    // 监听窗口大小变化，重新滚动到底部
    window.addEventListener('resize', this.scrollToBottom);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.scrollToBottom);
  }
}
</script>

<style scoped>
/* 滚动条样式优化 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 固定麦克风按钮样式 */
.fixed-mic-button {
  position: absolute;
  bottom: 80px; /* 位于输入框上方 */
  right: 20px;
  z-index: 50;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

/* 麦克风按钮悬停效果 */
.fixed-mic-button button {
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.fixed-mic-button button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* 麦克风按钮激活状态 */
.fixed-mic-button button.bg-red-500 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
</style>