<template>
  <div class="bg-white rounded-lg shadow-lg overflow-hidden h-full flex flex-col">
    <!-- 产品导航标签 -->
    <div class="flex text-white bg-gradient-to-r from-primary to-blue-500 overflow-x-auto whitespace-nowrap flex-shrink-0">
      <div 
        v-for="(tab, index) in tabs" 
        :key="index"
        @click="activeTab = index"
        :class="[
          'px-6 py-3 font-medium cursor-pointer transition-all duration-200',
          activeTab === index ? 'tab-active' : 'hover:bg-white hover:bg-opacity-10'
        ]"
      >
        {{ tab.name }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto content-scroll">
      <!-- 产品介绍 -->
      <div v-if="activeTab === 0" class="product-info p-6">
        <div class="product-banner text-white rounded-lg h-[300px] flex flex-col p-6 mb-6">
          <div class="flex justify-center mb-6">
            <div class="bg-white px-6 py-3 rounded-md">
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 100 100" class="text-brand mr-2">
                  <circle cx="50" cy="50" r="45" fill="#E60012"/>
                  <path d="M30,30 Q50,10 70,30 T80,60 Q60,80 40,70 T30,30" fill="#FFF" />
                </svg>
                <div>
                  <div class="text-primary text-xl font-bold">东吴人寿</div>
                  <div class="text-gray-500 text-xs uppercase">SOOCHOW LIFE</div>
                </div>
              </div>
            </div>
          </div>

          <h1 class="text-3xl font-bold text-center mb-4 tracking-wider">东吴盛期康宁 <span class="text-accent">(2024版)</span></h1>
          <h2 class="text-2xl font-bold text-center mb-6 tracking-wider">重大疾病保险</h2>
          
          <div class="bg-accent text-primary rounded-full py-3 px-8 text-center max-w-md mx-auto shadow-lg">
            <p class="text-lg font-bold">经典再续, 保障升级, 守护每一刻安宁!</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-light rounded-lg p-6 shadow">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-800">保障期限</h3>
            </div>
            <p class="text-gray-700">保障至被保险人终身，为您提供全生命周期的保障。</p>
          </div>

          <div class="bg-light rounded-lg p-6 shadow">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-800">疾病保障</h3>
            </div>
            <p class="text-gray-700">覆盖120种重大疾病、30种中症、40种轻症，保障全面。</p>
          </div>

          <div class="bg-light rounded-lg p-6 shadow">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-800">缴费期限</h3>
            </div>
            <p class="text-gray-700">灵活的缴费期限选择：10年、15年、20年、30年。</p>
          </div>

          <div class="bg-light rounded-lg p-6 shadow">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-800">额外保障</h3>
            </div>
            <p class="text-gray-700">身故保险金、全残保险金、轻症/中症多次赔付等特色保障。</p>
          </div>
        </div>
      </div>

      <!-- 产品条款 -->
      <div v-if="activeTab === 1" class="terms-content p-6">
        <div class="bg-gray-100 p-4 rounded-lg mb-6">
          <h3 class="text-lg font-bold text-gray-800 mb-2">东吴盛期康宁重大疾病保险条款</h3>
          <p class="text-gray-600 mb-4">条款编号：DWB1234-01</p>
          <p class="text-gray-700">
            本产品为保障型重大疾病保险产品，提供多种重大疾病保障、轻症和中症疾病保障。
            以下是相关条款摘要，详细内容请查看完整条款。
          </p>
        </div>
        
        <div class="border border-gray-200 rounded-lg mb-4">
          <div class="bg-gray-50 p-4 border-b border-gray-200">
            <h4 class="font-bold">第一章 保险责任</h4>
          </div>
          <div class="p-4">
            <p class="mb-2">在本合同保险期间内，我们承担下列保险责任：</p>
            <ol class="list-decimal list-inside pl-4 space-y-2">
              <li>重大疾病保险金</li>
              <li>中症疾病保险金</li>
              <li>轻症疾病保险金</li>
              <li>身故或全残保险金</li>
              <li>保险费豁免</li>
            </ol>
          </div>
        </div>
        
        <div class="border border-gray-200 rounded-lg">
          <div class="bg-gray-50 p-4 border-b border-gray-200">
            <h4 class="font-bold">第二章 责任免除</h4>
          </div>
          <div class="p-4">
            <p class="mb-2">因下列情形之一导致被保险人发生疾病、达到疾病状态或进行手术的，我们不承担给付保险金的责任：</p>
            <ol class="list-decimal list-inside pl-4 space-y-2">
              <li>投保人对被保险人的故意杀害、故意伤害</li>
              <li>被保险人故意自伤、故意犯罪或者抗拒依法采取的刑事强制措施</li>
              <li>被保险人主动吸食或注射毒品</li>
              <li>被保险人酒后驾驶机动车</li>
              <li>战争、军事冲突、暴乱或武装叛乱</li>
              <li>核爆炸、核辐射或核污染</li>
              <li>遗传性疾病，先天性畸形、变形或染色体异常</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- 理赔服务 -->
      <div v-if="activeTab === 2" class="p-6">
        <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
          <div class="bg-primary text-white p-4">
            <h3 class="text-xl font-bold">理赔流程</h3>
          </div>
          
          <div class="p-6">
            <div class="flex items-start mb-8">
              <div class="w-12 h-12 rounded-full bg-blue-100 text-primary flex items-center justify-center mr-4 flex-shrink-0">
                <span class="font-bold">1</span>
              </div>
              <div>
                <h4 class="text-lg font-bold text-gray-800 mb-1">报案</h4>
                <p class="text-gray-600">拨打我公司客服电话95586进行报案，或通过官方APP、微信小程序进行在线报案。</p>
              </div>
            </div>
            
            <div class="flex items-start mb-8">
              <div class="w-12 h-12 rounded-full bg-blue-100 text-primary flex items-center justify-center mr-4 flex-shrink-0">
                <span class="font-bold">2</span>
              </div>
              <div>
                <h4 class="text-lg font-bold text-gray-800 mb-1">提交材料</h4>
                <p class="text-gray-600">准备理赔所需资料，包括理赔申请书、病历资料、诊断证明、检查报告等材料。</p>
              </div>
            </div>
            
            <div class="flex items-start mb-8">
              <div class="w-12 h-12 rounded-full bg-blue-100 text-primary flex items-center justify-center mr-4 flex-shrink-0">
                <span class="font-bold">3</span>
              </div>
              <div>
                <h4 class="text-lg font-bold text-gray-800 mb-1">审核</h4>
                <p class="text-gray-600">我公司接收材料后，将尽快进行审核，必要时可能会进行调查。</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="w-12 h-12 rounded-full bg-blue-100 text-primary flex items-center justify-center mr-4 flex-shrink-0">
                <span class="font-bold">4</span>
              </div>
              <div>
                <h4 class="text-lg font-bold text-gray-800 mb-1">赔付</h4>
                <p class="text-gray-600">审核通过后，我公司将在规定时间内支付理赔款项到您指定的账户。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 保险案例 -->
      <div v-if="activeTab === 3" class="p-6">
        <div class="bg-light rounded-lg p-6 shadow-md mb-6">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800">李先生，35岁</h3>
          </div>
          
          <div class="border-l-4 border-primary pl-4 mb-4">
            <h4 class="font-bold text-gray-700 mb-1">投保情况</h4>
            <p class="text-gray-600">李先生于2022年投保"东吴盛期康宁重大疾病保险"，基本保额50万元，年缴保费12000元，缴费期20年。</p>
          </div>
          
          <div class="border-l-4 border-secondary pl-4">
            <h4 class="font-bold text-gray-700 mb-1">理赔情况</h4>
            <p class="text-gray-600">投保后第2年，李先生不幸被确诊为甲状腺癌（轻症），获得赔付基本保额的20%，即10万元，且后续保费豁免。</p>
          </div>
        </div>
        
        <div class="bg-light rounded-lg p-6 shadow-md">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-800">张女士，42岁</h3>
          </div>
          
          <div class="border-l-4 border-primary pl-4 mb-4">
            <h4 class="font-bold text-gray-700 mb-1">投保情况</h4>
            <p class="text-gray-600">张女士于2021年投保"东吴盛期康宁重大疾病保险"，基本保额60万元，年缴保费15000元，缴费期15年。</p>
          </div>
          
          <div class="border-l-4 border-secondary pl-4">
            <h4 class="font-bold text-gray-700 mb-1">理赔情况</h4>
            <p class="text-gray-600">投保后第3年，张女士确诊为乳腺癌（重疾），获得赔付基本保额的100%，即60万元，合同终止。</p>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div v-if="activeTab === 4" class="p-6">
        <div class="bg-light rounded-lg p-6 shadow-md mb-6">
          <h3 class="text-xl font-bold text-primary mb-4">常见问题解答</h3>
          
          <div class="mb-6">
            <div class="flex items-center mb-2">
              <div class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3 flex-shrink-0">
                <span class="font-bold">Q</span>
              </div>
              <h4 class="font-bold text-gray-800">什么是等待期？</h4>
            </div>
            <div class="ml-11">
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center mb-2">
                  <div class="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="font-bold">A</span>
                  </div>
                  <p class="text-gray-700">等待期是指保险合同生效后一段时间内，即使发生保险事故，保险公司也不承担赔偿责任的时期。本产品的等待期为180天。</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mb-6">
            <div class="flex items-center mb-2">
              <div class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3 flex-shrink-0">
                <span class="font-bold">Q</span>
              </div>
              <h4 class="font-bold text-gray-800">如何选择合适的保额？</h4>
            </div>
            <div class="ml-11">
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center mb-2">
                  <div class="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="font-bold">A</span>
                  </div>
                  <p class="text-gray-700">建议保额为年收入的5-10倍，或者医疗费用的2-3倍。具体可根据个人年龄、收入、家庭责任等因素综合考虑。</p>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <div class="flex items-center mb-2">
              <div class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3 flex-shrink-0">
                <span class="font-bold">Q</span>
              </div>
              <h4 class="font-bold text-gray-800">轻症和中症可以赔付几次？</h4>
            </div>
            <div class="ml-11">
              <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center mb-2">
                  <div class="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="font-bold">A</span>
                  </div>
                  <p class="text-gray-700">本产品中，轻症最多可赔付3次，中症最多可赔付2次，每次赔付基本保额的一定比例，且每次赔付的疾病种类不能相同。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InsuranceContent',
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '产品介绍' },
        { name: '产品条款' },
        { name: '理赔服务' },
        { name: '保险案例' },
        { name: '常见问题' }
      ]
    }
  }
}
</script>

<style scoped>
.product-banner {
  background: linear-gradient(180deg, #2196F3 0%, #0D47A1 100%);
  position: relative;
  overflow: hidden;
}

.product-banner::before {
  content: '';
  position: absolute;
  bottom: -100px;
  left: 0;
  right: 0;
  height: 300px;
  background: radial-gradient(ellipse at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  border-radius: 50%;
}

.tab-active {
  background-color: rgba(255, 255, 255, 0.2);
  border-bottom: 3px solid #FFC107;
}

/* 滚动条样式优化 */
.content-scroll::-webkit-scrollbar {
  width: 6px;
}

.content-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.content-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

.content-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style> 