<template>
  <div>
    <!-- 登录页面 -->
    <LoginPage
      v-if="!isConfigured"
      @login="handleLogin"
    />

    <!-- 投保系统主界面 -->
    <InsuranceLayout
      ref="insuranceLayoutRef"
      v-else
      :layoutMode="layoutMode"
      :messages="formattedMessages"
      :showInsuranceButton="showInsuranceButton"
      :isConnecting="isConnecting"
      :virtualmanKey="virtualmanKey"
      :sign="sign"
      :conversationMode="conversationMode"
      :microphoneActive="microphoneActive"
      :aiState="aiState"
      @call="startConversation"
      @send-message="sendMessage"
      @start-insurance="startInsurance"
      @end-call="stopConversation"
      @toggle-microphone="toggleMicrophone"
    />

    <!-- 调试信息 -->
    <div v-if="isConfigured && debug" class="fixed bottom-2 left-2 bg-black bg-opacity-70 text-white p-2 text-xs rounded" style="z-index: 999;">
      连接状态: {{ connected ? '已连接' : (isConnecting ? '连接中' : '未连接') }}<br>
      消息数量: {{ messages.length }}<br>
      布局模式: {{ layoutMode }}<br>
      对话方式: {{ conversationMode === 'realtime' ? '实时通话' : '语音对讲' }}<br>
      AudioWorklet降噪: {{ audioWorkletEnabled ? '已启用' : '已禁用' }}<br>
      音频状态: {{ audioProcessorStatus }}<br>
      音频音量: {{ currentVolume.toFixed(3) }}<br>
      <span v-if="audioWorkletEnabled">
        VAD状态: 语音{{ vadDebugInfo.speechFrames }}帧 静音{{ vadDebugInfo.silenceFrames }}帧 比例{{ vadDebugInfo.voiceRatio.toFixed(2) }}
      </span>
      <div class="mt-2 flex flex-wrap gap-1">
        <button
          @click="addTestMessage('ai')"
          class="bg-blue-500 text-white px-2 py-1 rounded text-xs"
        >
          测试AI消息
        </button>
        <button
          @click="addTestMessage('user')"
          class="bg-green-500 text-white px-2 py-1 rounded text-xs"
        >
          测试用户消息
        </button>
        <button
          @click="switchLayout()"
          class="bg-purple-500 text-white px-2 py-1 rounded text-xs"
        >
          切换模式
        </button>
        <button
          @click="switchConversationMode()"
          class="bg-orange-500 text-white px-2 py-1 rounded text-xs"
        >
          切换对话方式
        </button>
        <button
          @click="toggleAudioWorklet()"
          :class="audioWorkletEnabled ? 'bg-green-600' : 'bg-red-600'"
          class="text-white px-2 py-1 rounded text-xs"
        >
          {{ audioWorkletEnabled ? '关闭' : '开启' }}降噪
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import LoginPage from './components/LoginPage.vue';
import InsuranceLayout from './components/InsuranceLayout.vue';
import type TencentDigitalHuman from './components/TencentDigitalHuman.vue'; // 类型导入
import { TRTC_API } from './config/api';
import { AudioProcessor } from './utils/audioProcessor.ts';

// 声明TRTC全局变量
declare const TRTC: any;

// 声明全局IVH对象类型
declare global {
  interface Window {
    IVH: {
      init: (options: any) => void;
      on: (event: string, callback: Function) => void;
      createSession: (options: any) => Promise<any>;
      startSession: () => Promise<any>;
      closeSession: () => Promise<void>;
      play: (options: any) => Promise<any>;
      stop: () => void;
    };
  }
}

// Refs for component instances
const insuranceLayoutRef = ref<InstanceType<typeof InsuranceLayout> | null>(null);
const digitalHumanRef = ref<InstanceType<typeof TencentDigitalHuman> | null>(null);

// Data properties
const accessToken = ref('');
const botId = ref('');
const isConfigured = ref(false); // 默认为已配置，不需要登录
const audioPermGranted = ref(false);
const trtcClient = ref<any>(null);
const connected = ref(false);
const isConnecting = ref(false);
const messages = ref<Array<{ id?: string; type: string; content: string; sender?: string; end?: boolean }>>([]);
const layoutMode = ref<'initial' | 'conversation' | 'insurance'>('initial');
const showInsuranceButton = ref(false);
const taskId = ref<string | null>(null);
const debug = ref(true); // 调试模式
const mouthAnimationInterval = ref<ReturnType<typeof setInterval> | null>(null); // 用于存储嘴唇动画的定时器ID
const streamSeq = ref(1); // 用于维护流式文本的序列号
const streamReqId = ref(''); // 用于维护流式文本的请求ID
// 对话方式：realtime-实时通话，intercom-语音对讲
const conversationMode = ref<'realtime' | 'intercom'>('intercom');
// 麦克风状态
const microphoneActive = ref(false);
// AI状态：1-聆听中，2-思考中，3-说话中，4-被打断
const aiState = ref<number>(0);

// AudioWorklet降噪相关状态
const audioWorkletEnabled = ref(false);
const audioProcessor = ref<AudioProcessor | null>(null);
const audioProcessorStatus = ref('未初始化');
const currentVolume = ref(0);
const vadDebugInfo = ref({
  speechFrames: 0,
  silenceFrames: 0,
  voiceRatio: 0,
  energy: 0
});

// 腾讯数智人配置
const virtualmanKey = ref(''); // 腾讯数智人项目ID，实际使用时需要填入真实的值
const sign = ref(''); // 腾讯数智人签名，实际使用时需要填入真实的值

// Computed properties
const formattedMessages = computed(() => {
  return messages.value.map(msg => ({
    type: msg.type,
    content: msg.content
  }));
});

// Watch for InsuranceLayout component to be mounted and try to get TencentDigitalHuman instance
watch(insuranceLayoutRef, (newLayoutInstance) => {
  if (newLayoutInstance) {
    console.log('[App.vue] InsuranceLayout实例已获取，尝试获取数字人实例');

    // 尝试获取数字人实例
    if (typeof (newLayoutInstance as any).getDigitalHumanInstance === 'function') {
      digitalHumanRef.value = (newLayoutInstance as any).getDigitalHumanInstance();
      if (digitalHumanRef.value) {
        console.log('[App.vue] 成功获取数字人实例');
      } else {
        console.warn('[App.vue] 获取数字人实例失败，可能尚未初始化');

        // 设置一个定时器，稍后再次尝试获取数字人实例
        setTimeout(() => {
          if (insuranceLayoutRef.value) {
            digitalHumanRef.value = (insuranceLayoutRef.value as any).getDigitalHumanInstance();
            if (digitalHumanRef.value) {
              console.log('[App.vue] 延迟获取数字人实例成功');
            } else {
              console.warn('[App.vue] 延迟获取数字人实例仍然失败');
            }
          }
        }, 3000);
      }
    } else {
      console.warn('[App.vue] InsuranceLayout没有提供getDigitalHumanInstance方法');
    }
  }
});


// Methods
const handleLogin = (credentials: { accessToken: string; botId: string; virtualmanKey: string; sign: string }) => {
  accessToken.value = credentials.accessToken;
  botId.value = credentials.botId;
  virtualmanKey.value = credentials.virtualmanKey;
  sign.value = credentials.sign;
  isConfigured.value = true;
  checkAudioPermission();
};

const checkAudioPermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    audioPermGranted.value = true;
    stream.getTracks().forEach(track => track.stop());
  } catch (error) {
    audioPermGranted.value = false;
    alert('请允许访问麦克风权限以便能够与AI助手进行语音交流');
    console.error('麦克风权限获取失败:', error);
  }
};
// 配置信息 - 随机生成roomId、userId和robotId
const generateRandomId = (prefix: string): string => {
  return `${prefix}_${Math.random().toString(36).substring(2, 10)}`;
};

const roomId = Math.floor(100000 + Math.random() * 900000); // 6位随机数
const userId = generateRandomId('user');
const robotId = generateRandomId('ai');
const sessionId = generateRandomId('lke-');
// 启动AI对话
async function startConversation() {
  if (!audioPermGranted.value) {
    await checkAudioPermission();
    if (!audioPermGranted.value) return;
  }

  isConnecting.value = true;

  // 获取数字人实例，确保在启动对话前已经获取到数字人实例
  if (!digitalHumanRef.value && insuranceLayoutRef.value) {
    digitalHumanRef.value = (insuranceLayoutRef.value as any).getDigitalHumanInstance();
    console.log('[App.vue] 在启动对话前获取数字人实例:', digitalHumanRef.value ? '成功' : '失败');
  }

  try {
    // 创建TRTC客户端
    trtcClient.value = trtcClient.value || TRTC.create();

    // 用于存储上一次接收到的文本，用于计算增量
    let lastReceivedText = '';

    // 监听远端视频流事件
    trtcClient.value.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
      console.log('[App.vue] 收到远端视频流事件:', { userId, streamType });

      // 检查是否是AI机器人的视频流
      if (userId === robotId) {
        console.log('[App.vue] 检测到AI机器人的视频流，开始播放远端视频');

        // 获取数字人实例并设置为TRTC模式
        if (digitalHumanRef.value) {
          // 创建视频容器ID
          const videoContainerId = `trtc-remote-video-${userId}-${streamType}`;

          // 通知数字人组件切换到TRTC模式并提供容器ID
          if (typeof digitalHumanRef.value.switchToTRTCMode === 'function') {
            digitalHumanRef.value.switchToTRTCMode(videoContainerId);
          }

          // 启动远端视频播放
          trtcClient.value.startRemoteVideo({
            userId,
            streamType,
            view: videoContainerId
          }).then(() => {
            console.log('[App.vue] 远端视频播放启动成功');
          }).catch((error) => {
            console.error('[App.vue] 远端视频播放启动失败:', error);
          });
        } else {
          console.warn('[App.vue] 数字人实例不可用，无法播放远端视频');
        }
      }
    });

    // 监听远端视频流停止事件
    trtcClient.value.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, ({ userId, streamType }) => {
      console.log('[App.vue] 远端视频流停止事件:', { userId, streamType });

      // 检查是否是AI机器人的视频流
      if (userId === robotId) {
        console.log('[App.vue] AI机器人视频流停止，切换回数智人模式');

        // 通知数字人组件切换回数智人模式
        if (digitalHumanRef.value && typeof digitalHumanRef.value.switchToIVHMode === 'function') {
          digitalHumanRef.value.switchToIVHMode();
        }
      }
    });

    // 监听自定义消息
    trtcClient.value.on(TRTC.EVENT.CUSTOM_MESSAGE, (event: any) => {
      let jsonData = new TextDecoder().decode(event.data);
      let data = JSON.parse(jsonData);

      console.log('[App.vue] 收到TRTC自定义消息:', data);

      // 处理文本消息
      if (data.type === 10000) {
        const sender = data.sender;
        const currentText = data.payload.text;
        const roundId = data.payload.roundid;
        const isRobot = sender.includes('ai_');
        const end = data.payload.end; // robot动态展示文字，不显示省略号

        console.log('[App.vue] 处理文本消息:', {
          sender,
          currentText,
          roundId,
          isRobot,
          end,
          messagesLength: messages.value.length
        });

        // 处理消息显示
        const msgItem = messages.value.find(item => item.id === roundId && item.sender === sender);
        if (msgItem) {
          console.log('[App.vue] 更新现有消息:', msgItem.content, '->', currentText);
          msgItem.content = currentText;
          msgItem.end = end;
        } else {
          console.log('[App.vue] 添加新消息:', currentText);
          messages.value.push({
            id: roundId,
            content: currentText,
            sender,
            type: isRobot ? 'ai' : 'user',
            end: end
          });

          // 如果是新消息，重置lastReceivedText
          if (isRobot) {
            lastReceivedText = '';
          }
        }

        // 检查当前数字人模式，只在IVH模式下手动控制嘴型
        const isUsingTRTCMode = digitalHumanRef.value &&
          typeof digitalHumanRef.value.getCurrentMode === 'function' &&
          digitalHumanRef.value.getCurrentMode() === 'trtc';

        // 如果是AI消息且正在说话，根据模式决定是否启动嘴型动画
        if (isRobot && !end) {
          if (!isUsingTRTCMode) {
            // 只在IVH模式下手动控制嘴型动画
            // 计算增量文本
            let incrementalText = '';
            if (currentText.startsWith(lastReceivedText)) {
              incrementalText = currentText.substring(lastReceivedText.length);
            } else {
              // 如果当前文本不是上一次文本的延续，则使用整个文本
              incrementalText = currentText;
            }

            // 更新lastReceivedText
            lastReceivedText = currentText;

            // 只有当增量文本不为空时才发送
            if (incrementalText) {
              console.log('[App.vue] IVH模式：发送增量文本到数字人:', incrementalText, '(完整文本:', currentText, ')');
              // 将AI响应的增量文本发送给数字人，驱动嘴型动画
              startMouthAnimation(incrementalText, !end);
            }
          } else {
            console.log('[App.vue] TRTC模式：跳过手动嘴型控制，使用远端视频流自带的音视频同步');
            // 更新lastReceivedText以保持状态一致
            lastReceivedText = currentText;
          }
        }
        // 如果是AI消息且已结束，根据模式决定是否停止嘴型动画
        else if (isRobot && end) {
          // 重置lastReceivedText
          lastReceivedText = '';

          if (!isUsingTRTCMode) {
            // 只在IVH模式下停止手动嘴型动画
            console.log('[App.vue] IVH模式：停止嘴型动画');
            stopMouthAnimation();
          } else {
            console.log('[App.vue] TRTC模式：无需手动停止嘴型动画');
          }

          // 如果是语音对讲模式，AI说完话后自动关闭麦克风
          if (conversationMode.value === 'intercom' && microphoneActive.value) {
            toggleMicrophone();
          }
        }
      }

      // 处理AI状态回调消息
      else if (data.type === 10001) {
        console.log('[App.vue] 收到AI状态回调:', data);
        const state = data.payload.state;
        aiState.value = state;

        // 如果是语音对讲模式，且AI被打断或思考中，自动关闭麦克风
        if (conversationMode.value === 'intercom' && microphoneActive.value && (state === 2 || state === 4)) {
          toggleMicrophone();
        }
      }
    });

    // 调用后端API启动对话
    const data = {
      "roomId": roomId,
      "userId": userId,
      "robotId": robotId,
      "sessionId": sessionId
    };

    const res = await startAIConversation(JSON.stringify(data));
    // 进入房间
    await trtcClient.value.enterRoom({
      roomId,
      scene: "rtc",
      sdkAppId: res.data.sdkAppId,
      userId,
      userSig: res.data.userSig,
    });
    // 根据对话方式决定是否启动麦克风
    if (conversationMode.value === 'realtime') {
      // 实时通话模式，自动启动麦克风
      if (audioWorkletEnabled.value) {
        // 使用AudioWorklet处理音频
        await startAudioWithWorklet();
      } else {
        // 直接启动TRTC音频
        await trtcClient.value.startLocalAudio();
      }
      microphoneActive.value = true;
    } else {
      // 语音对讲模式，默认不启动麦克风
      microphoneActive.value = false;
    }

    taskId.value = res.data.TaskId;

    // 更新状态
    connected.value = true;
    isConnecting.value = false;

    // 切换到对话模式
    setTimeout(() => {
      layoutMode.value = 'conversation';
      setTimeout(() => {
        showInsuranceButton.value = true;
      }, 3000);
    }, 300);

  } catch (error) {
    console.error('启动对话失败:', error);
    stopConversation();
    isConnecting.value = false;
    alert('启动对话失败，请重试');
  }
}

// 停止AI对话
async function stopConversation() {
  try {
    console.log('[App.vue] 停止AI对话，当前布局模式:', layoutMode.value);

    // 停止对话
    if (taskId.value) {
      await stopAIConversation(JSON.stringify({
        taskId: taskId.value,
      }));
      taskId.value = null;
    }

    // 退出房间
    if (trtcClient.value) {
      await trtcClient.value.exitRoom();
      trtcClient.value.destroy();
      trtcClient.value = null;
    }

    // 停止嘴型动画
    stopMouthAnimation();

    // 切换数字人回到IVH模式
    if (digitalHumanRef.value && typeof digitalHumanRef.value.switchToIVHMode === 'function') {
      digitalHumanRef.value.switchToIVHMode();
    }

    // 更新状态
    connected.value = false;
    isConnecting.value = false;

    console.log('[App.vue] 切换回初始布局模式');
    layoutMode.value = 'initial';
    showInsuranceButton.value = false;

    // 清空消息列表
    messages.value = [];

    console.log('[App.vue] 停止AI对话完成，当前布局模式:', layoutMode.value);
  } catch (error) {
    console.error('停止对话失败:', error);

    console.log('[App.vue] 出错后强制切换回初始布局模式');
    layoutMode.value = 'initial';
    showInsuranceButton.value = false;
  }
}

// 启动嘴型动画
async function startMouthAnimation(text?: string, isStreaming: boolean = true) {
  console.log('[App.vue] Voice playback started. Starting mouth animation.');

  // 如果没有提供文本，则不执行任何操作
  if (!text) {
    console.warn('[App.vue] 没有提供文本，无法启动嘴型动画');
    return;
  }

  // 获取数字人实例
  if (!digitalHumanRef.value && insuranceLayoutRef.value) {
    digitalHumanRef.value = (insuranceLayoutRef.value as any).getDigitalHumanInstance();
    console.log('[App.vue] 获取数字人实例:', digitalHumanRef.value ? '成功' : '失败');
  }

  // 如果是新的对话，或者reqId为空，则生成新的reqId
  if (!streamReqId.value) {
    streamReqId.value = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    streamSeq.value = 1; // 重置序列号
    console.log('[App.vue] 生成新的reqId:', streamReqId.value);
  }

  // 使用数字人实例发送流式文本，驱动嘴型动画
  if (digitalHumanRef.value) {
    try {
      // 确定是否是最后一个片段
      const isFinal = !isStreaming;

      console.log('[App.vue] 发送流式文本到数字人:', text, '序列号:', streamSeq.value, 'reqId:', streamReqId.value, 'isFinal:', isFinal);

      // 使用sendStreamText方法发送文本，驱动嘴型动画但不发声
      if (typeof digitalHumanRef.value.sendStreamText === 'function') {
        // 传递seq、reqId和isFinal
        await digitalHumanRef.value.sendStreamText(text, streamSeq.value, streamReqId.value, isFinal);

        // 增加序列号，为下一次调用做准备
        streamSeq.value++;
      } else {
        console.error('[App.vue] 数字人实例没有sendStreamText方法');
      }
    } catch (error) {
      console.error('[App.vue] 发送流式文本到数字人失败:', error);

      // 如果发送失败，重置reqId，下次将使用新的reqId
      streamReqId.value = '';
    }
  } else {
    console.warn('[App.vue] 数字人实例不可用，无法启动嘴型动画');

    // 尝试通过DOM查找数字人实例
    const digitalHumanElement = document.querySelector('.digital-human-container');
    if (digitalHumanElement && (digitalHumanElement as any).__vueParentComponent?.ctx) {
      console.log('[App.vue] 通过DOM找到数字人实例');
      digitalHumanRef.value = (digitalHumanElement as any).__vueParentComponent.ctx;

      // 重试发送文本
      try {
        // 确定是否是最后一个片段
        const isFinal = !isStreaming;

        if (typeof digitalHumanRef.value.sendStreamText === 'function') {
          // 传递seq、reqId和isFinal
          await digitalHumanRef.value.sendStreamText(text, streamSeq.value, streamReqId.value, isFinal);

          // 增加序列号，为下一次调用做准备
          streamSeq.value++;
        } else {
          console.error('[App.vue] 数字人实例没有sendStreamText方法');
        }
      } catch (retryError) {
        console.error('[App.vue] 重试发送文本失败:', retryError);

        // 如果发送失败，重置reqId，下次将使用新的reqId
        streamReqId.value = '';
      }
    }
  }
}

// 停止嘴型动画
async function stopMouthAnimation() {
  console.log('[App.vue] Voice playback ended. Stopping mouth animation.');

  // 重置reqId和seq，下次将使用新的reqId和seq
  streamReqId.value = '';
  streamSeq.value = 1;
  console.log('[App.vue] 重置reqId和seq');

  // 获取数字人实例
  if (!digitalHumanRef.value && insuranceLayoutRef.value) {
    digitalHumanRef.value = (insuranceLayoutRef.value as any).getDigitalHumanInstance();
    console.log('[App.vue] 获取数字人实例:', digitalHumanRef.value ? '成功' : '失败');
  }

  // 使用数字人实例停止当前播放
  if (digitalHumanRef.value) {
    try {
      console.log('[App.vue] 停止数字人动画');

      // 检查stopPlaying方法是否存在
      if (typeof digitalHumanRef.value.stopPlaying === 'function') {
        // 使用stopPlaying方法停止当前播放
        await digitalHumanRef.value.stopPlaying();
      } else {
        // 如果stopPlaying方法不存在，尝试直接调用IVH.stop
        console.warn('[App.vue] stopPlaying方法不存在，尝试使用window.IVH.stop');
        if (window.IVH && typeof window.IVH.stop === 'function') {
          window.IVH.stop();
        } else {
          console.error('[App.vue] 无法停止数字人动画，没有可用的停止方法');
        }
      }
    } catch (error) {
      console.error('[App.vue] 停止数字人动画失败:', error);
    }
  } else {
    console.warn('[App.vue] 数字人实例不可用，无法停止嘴型动画');

    // 尝试通过DOM查找数字人实例
    const digitalHumanElement = document.querySelector('.digital-human-container');
    if (digitalHumanElement && (digitalHumanElement as any).__vueParentComponent?.ctx) {
      console.log('[App.vue] 通过DOM找到数字人实例');
      digitalHumanRef.value = (digitalHumanElement as any).__vueParentComponent.ctx;

      // 重试停止播放
      try {
        if (typeof digitalHumanRef.value.stopPlaying === 'function') {
          await digitalHumanRef.value.stopPlaying();
        } else if (window.IVH && typeof window.IVH.stop === 'function') {
          window.IVH.stop();
        }
      } catch (retryError) {
        console.error('[App.vue] 重试停止播放失败:', retryError);
      }
    } else if (window.IVH && typeof window.IVH.stop === 'function') {
      // 如果无法获取数字人实例，直接尝试使用全局IVH对象
      console.log('[App.vue] 直接使用window.IVH.stop停止播放');
      window.IVH.stop();
    }
  }
}

// 发送消息
const sendMessage = (message: string) => {
  if (connected.value && message.trim()) {
    console.log('发送消息:', message);
    // 消息会通过TRTC的自定义消息机制返回，不需要额外处理

    const send_message = {
      "type": 20000,
      "sender": userId,
      "receiver": [robotId],
      "payload": {
        "id": new Date().getTime()+"",
        "timestamp": new Date().getTime(),
        "message": message
      }
    };
    console.log('发送自定义消息:', JSON.stringify(send_message));
    trtcClient.value.sendCustomMessage({
      cmdId: 2,
      data: new TextEncoder().encode(JSON.stringify(send_message)).buffer
    });
    // 服务端发送自定义消息
    // if (taskId.value) {
    //   controlAIConversation(JSON.stringify({
    //     taskId: taskId.value,
    //     command: "ServerPushText",
    //     useLLM: true,
    //     sessionId: sessionId,
    //     serverPushText: {
    //         text: message,
    //         interrupt: true
    //     }
    //   }));
    // }
  }
};

// 调用后端API启动AI对话
async function startAIConversation(data: string) {
  const response = await fetch(TRTC_API.START_CONVERSATION, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: data,
  });

  if (!response.ok) {
    throw new Error(`启动对话失败: ${response.status}`);
  }

  return await response.json();
}

// 调用后端API停止AI对话
async function stopAIConversation(data: string) {
  const response = await fetch(TRTC_API.STOP_CONVERSATION, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: data,
  });

  if (!response.ok) {
    throw new Error(`停止对话失败: ${response.status}`);
  }

  return await response.json();
}

// 调用后端API控制AI对话
async function controlAIConversation(data: string) {
  const response = await fetch(TRTC_API.CONTROL_CONVERSATION, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: data,
  });

  if (!response.ok) {
    throw new Error(`控制对话失败: ${response.status}`);
  }

  return await response.json();
}

const startInsurance = () => {
  console.log('开始投保流程');
  setTimeout(() => {
    layoutMode.value = 'insurance';
  }, 50);
};

const resetApp = () => {
  stopConversation();
  messages.value = [];
  layoutMode.value = 'initial';
  showInsuranceButton.value = false;
};

const addTestMessage = (type: 'ai' | 'user') => {
  const testMessages = {
    'ai': '这是一条来自AI的测试消息，用于验证对话框组件是否正常显示。',
    'user': '这是一条来自用户的测试消息，用于验证对话框组件是否正常显示。'
  };
  messages.value.push({
    type: type,
    content: testMessages[type] + ' (时间戳: ' + new Date().toLocaleTimeString() + ')'
  });
  console.log('添加测试消息:', type, '消息总数:', messages.value.length);
  if (layoutMode.value === 'initial') {
    setTimeout(() => {
      layoutMode.value = 'conversation';
      setTimeout(() => {
        showInsuranceButton.value = true;
      }, 2000);
    }, 50);
  }
};

const switchLayout = () => {
  const layouts = ['initial', 'conversation', 'insurance'];
  const currentIndex = layouts.indexOf(layoutMode.value);
  const nextIndex = (currentIndex + 1) % layouts.length;
  const nextLayout = layouts[nextIndex] as 'initial' | 'conversation' | 'insurance';

  if (nextLayout !== 'conversation') {
    showInsuranceButton.value = false;
  }
  layoutMode.value = nextLayout;
  console.log('切换布局模式为:', layoutMode.value);
  if (nextLayout === 'conversation') {
    setTimeout(() => {
      showInsuranceButton.value = true;
    }, 1000);
  }
};

// 切换对话方式（实时通话/语音对讲）
const switchConversationMode = () => {
  // 如果当前是实时通话，切换到语音对讲
  if (conversationMode.value === 'realtime') {
    conversationMode.value = 'intercom';
    console.log('切换到语音对讲模式');

    // 如果正在通话中，停止麦克风
    if (connected.value && trtcClient.value) {
      trtcClient.value.stopLocalAudio();
      microphoneActive.value = false;
    }
  }
  // 如果当前是语音对讲，切换到实时通话
  else {
    conversationMode.value = 'realtime';
    console.log('切换到实时通话模式');

    // 如果正在通话中，启动麦克风
    if (connected.value && trtcClient.value) {
      trtcClient.value.startLocalAudio();
      microphoneActive.value = true;
    }
  }
};

// 控制麦克风（仅在语音对讲模式下使用）
const toggleMicrophone = async () => {
  if (!connected.value || !trtcClient.value) return;

  // 如果是实时通话模式，不做任何操作
  if (conversationMode.value === 'realtime') return;

  if (microphoneActive.value) {
    // 关闭麦克风
    await trtcClient.value.stopLocalAudio();
    microphoneActive.value = false;
    console.log('麦克风已关闭');
    // 重置AI状态
    aiState.value = 0;

    // 如果启用了AudioWorklet，停止处理
    if (audioWorkletEnabled.value && audioProcessor.value) {
      audioProcessor.value.stopProcessing();
    }
  } else {
    // 打开麦克风
    if (audioWorkletEnabled.value) {
      // 使用AudioWorklet处理音频
      await startAudioWithWorklet();
    } else {
      // 直接启动TRTC音频
      await trtcClient.value.startLocalAudio();
    }
    microphoneActive.value = true;
    console.log('麦克风已打开');
    // 设置AI状态为聆听中
    aiState.value = 1;
  }
};

// AudioWorklet相关方法
const initializeAudioProcessor = async () => {
  if (!AudioProcessor.isSupported()) {
    console.error('[App.vue] 浏览器不支持AudioWorklet');
    audioProcessorStatus.value = '不支持';
    return false;
  }

  try {
    audioProcessor.value = new AudioProcessor();

    // 设置回调函数
    audioProcessor.value.setCallbacks({
      onVoiceActivityChanged: (isActive: boolean) => {
        console.log('[App.vue] 语音活动状态变化:', isActive);
        // 注意：现在AudioWorklet处理的音频流直接提供给TRTC
        // 所以TRTC的语音识别会自动受益于我们的降噪和VAD处理
        // 不需要手动控制麦克风开关，让TRTC遵循原有逻辑即可
      },
      onAudioAnalysis: (data: any) => {
        // 更新VAD调试信息
        vadDebugInfo.value = {
          speechFrames: data.speechFrames || 0,
          silenceFrames: data.silenceFrames || 0,
          voiceRatio: data.voiceRatio || 0,
          energy: data.energy || 0
        };

        // 可以在这里处理音频分析数据，用于调试
        if (data.energy > 0.02) {
          console.log('[App.vue] 音频分析:', {
            energy: data.energy.toFixed(4),
            isVoiceActive: data.isVoiceActive,
            speechFrames: data.speechFrames,
            silenceFrames: data.silenceFrames,
            voiceRatio: data.voiceRatio?.toFixed(2)
          });
        }
      },
      onStatusChanged: (status: string) => {
        audioProcessorStatus.value = status;
      },
      onVolumeChanged: (volume: number) => {
        currentVolume.value = volume;
      }
    });

    const success = await audioProcessor.value.initialize();
    return success;
  } catch (error) {
    console.error('[App.vue] AudioProcessor初始化失败:', error);
    audioProcessorStatus.value = '初始化失败';
    return false;
  }
};

const startAudioWithWorklet = async () => {
  if (!audioProcessor.value) {
    console.error('[App.vue] AudioProcessor未初始化');
    return;
  }

  try {
    // 获取原始音频流
    const originalStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: false,
        noiseSuppression: false,
        autoGainControl: false
      }
    });

    // 通过AudioWorklet处理音频
    const processedStream = await audioProcessor.value.startProcessing(originalStream);

    if (processedStream) {
      // 让TRTC使用AudioWorklet处理后的音频流
      console.log('[App.vue] 使用AudioWorklet处理后的音频流启动TRTC');

      // 尝试让TRTC使用处理后的音频流
      // 注意：这可能需要TRTC支持自定义音频流
      await trtcClient.value.startLocalAudio({
        microphoneId: '', // 不指定设备ID
        mediaStream: processedStream // 使用处理后的流
      });

      console.log('[App.vue] TRTC使用AudioWorklet处理后的音频流成功');
    } else {
      console.error('[App.vue] AudioWorklet处理音频失败，回退到直接模式');
      await trtcClient.value.startLocalAudio();
    }
  } catch (error) {
    console.error('[App.vue] AudioWorklet音频启动失败:', error);
    console.log('[App.vue] 回退到直接模式');
    // 回退到直接模式
    await trtcClient.value.startLocalAudio();
  }
};

const toggleAudioWorklet = async () => {
  if (audioWorkletEnabled.value) {
    // 关闭AudioWorklet
    audioWorkletEnabled.value = false;
    if (audioProcessor.value) {
      audioProcessor.value.destroy();
      audioProcessor.value = null;
    }
    console.log('[App.vue] AudioWorklet已关闭');
  } else {
    // 启用AudioWorklet
    const success = await initializeAudioProcessor();
    if (success) {
      audioWorkletEnabled.value = true;
      console.log('[App.vue] AudioWorklet已启用');
    } else {
      console.error('[App.vue] AudioWorklet启用失败');
    }
  }
};

onMounted(() => {
  console.log('App组件已挂载');
  if (isConfigured.value) {
    checkAudioPermission();
  }
});

onBeforeUnmount(() => {
  stopConversation();
  if (mouthAnimationInterval.value) {
    clearInterval(mouthAnimationInterval.value);
    mouthAnimationInterval.value = null;
  }

  // 清理AudioProcessor
  if (audioProcessor.value) {
    audioProcessor.value.destroy();
    audioProcessor.value = null;
  }
});

</script>

<style>
/* Tailwind已通过index.css导入，这里不再重复导入 */
body {
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #F2F7FF;
}

:root {
  --color-primary: #1C3F94;
  --color-secondary: #FF5722;
  --color-brand: #E60012;
  --color-accent: #FFC107;
  --color-light: #F2F7FF;
}
</style>