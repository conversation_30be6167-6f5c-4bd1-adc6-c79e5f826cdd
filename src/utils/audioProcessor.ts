// AudioWorklet注册工具函数
const createWorkletFromSrc = (workletName: string, workletSrc: string) => {
  const script = new Blob(
    [`registerProcessor("${workletName}", ${workletSrc})`],
    {
      type: "application/javascript",
    },
  );
  return URL.createObjectURL(script);
};

// 音频处理器管理类
export class AudioProcessor {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private destinationNode: MediaStreamAudioDestinationNode | null = null;
  private volumeWorkletNode: AudioWorkletNode | null = null;
  private isInitialized = false;
  private isProcessing = false;

  // 回调函数
  private onVoiceActivityChanged?: (isActive: boolean) => void;
  private onAudioAnalysis?: (data: any) => void;
  private onStatusChanged?: (status: string) => void;
  private onVolumeChanged?: (volume: number) => void;

  // VAD参数 - 参考live-api-web-console优化
  private vadParams = {
    vadThreshold: 0.02,        // 提高语音检测阈值
    silenceThreshold: 0.005,   // 静音阈值
    noiseGateThreshold: 0.008, // 噪声门限
    noiseReduction: 0.3,       // 降低噪声抑制强度
    minSpeechFrames: 20,       // 增加最少语音帧数，防止误触发
    minSilenceFrames: 60,      // 增加最少静音帧数，防止过早结束
    maxSilenceFrames: 200,     // 增加最大静音帧数
    smoothingFactor: 0.7       // 音量平滑因子
  };

  constructor() {
    this.updateStatus('未初始化');
  }

  // 设置回调函数
  setCallbacks(callbacks: {
    onVoiceActivityChanged?: (isActive: boolean) => void;
    onAudioAnalysis?: (data: any) => void;
    onStatusChanged?: (status: string) => void;
    onVolumeChanged?: (volume: number) => void;
  }) {
    this.onVoiceActivityChanged = callbacks.onVoiceActivityChanged;
    this.onAudioAnalysis = callbacks.onAudioAnalysis;
    this.onStatusChanged = callbacks.onStatusChanged;
    this.onVolumeChanged = callbacks.onVolumeChanged;
  }

  // 更新状态
  private updateStatus(status: string) {
    console.log('[AudioProcessor] 状态更新:', status);
    if (this.onStatusChanged) {
      this.onStatusChanged(status);
    }
  }

  // 初始化AudioWorklet
  async initialize(): Promise<boolean> {
    try {
      this.updateStatus('初始化中...');

      // 创建AudioContext
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // 加载VAD处理器
      const vadWorkletUrl = createWorkletFromSrc('vad-processor', this.getVADWorkletCode());
      await this.audioContext.audioWorklet.addModule(vadWorkletUrl);

      // 加载音量计处理器
      const volumeWorkletUrl = createWorkletFromSrc('volume-meter', this.getVolumeMeterWorkletCode());
      await this.audioContext.audioWorklet.addModule(volumeWorkletUrl);

      this.isInitialized = true;
      this.updateStatus('已初始化');
      return true;
    } catch (error) {
      console.error('[AudioProcessor] 初始化失败:', error);
      this.updateStatus('初始化失败');
      return false;
    }
  }

  // 开始处理音频
  async startProcessing(inputStream: MediaStream): Promise<MediaStream | null> {
    if (!this.isInitialized || !this.audioContext) {
      console.error('[AudioProcessor] 未初始化');
      return null;
    }

    try {
      this.updateStatus('启动处理中...');

      // 创建音频源节点
      this.mediaStream = inputStream;
      this.sourceNode = this.audioContext.createMediaStreamSource(inputStream);

      // 创建VAD处理节点
      this.workletNode = new AudioWorkletNode(this.audioContext, 'vad-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        channelCount: 1
      });

      // 创建音量计节点
      this.volumeWorkletNode = new AudioWorkletNode(this.audioContext, 'volume-meter', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        channelCount: 1
      });

      // 创建目标节点
      this.destinationNode = this.audioContext.createMediaStreamDestination();

      // 连接音频节点：source -> VAD -> volume -> destination
      this.sourceNode.connect(this.workletNode);
      this.workletNode.connect(this.volumeWorkletNode);
      this.volumeWorkletNode.connect(this.destinationNode);

      // 设置VAD消息监听
      this.workletNode.port.onmessage = (event) => {
        this.handleVADMessage(event.data);
      };

      // 设置音量消息监听
      this.volumeWorkletNode.port.onmessage = (event) => {
        this.handleVolumeMessage(event.data);
      };

      // 发送初始参数
      this.updateVADParameters(this.vadParams);

      this.isProcessing = true;
      this.updateStatus('处理中');

      return this.destinationNode.stream;
    } catch (error) {
      console.error('[AudioProcessor] 启动处理失败:', error);
      this.updateStatus('启动失败');
      return null;
    }
  }

  // 停止处理
  stopProcessing() {
    try {
      this.updateStatus('停止处理中...');

      if (this.sourceNode) {
        this.sourceNode.disconnect();
        this.sourceNode = null;
      }

      if (this.workletNode) {
        this.workletNode.disconnect();
        this.workletNode = null;
      }

      if (this.volumeWorkletNode) {
        this.volumeWorkletNode.disconnect();
        this.volumeWorkletNode = null;
      }

      if (this.destinationNode) {
        this.destinationNode = null;
      }

      this.isProcessing = false;
      this.updateStatus('已停止');
    } catch (error) {
      console.error('[AudioProcessor] 停止处理失败:', error);
      this.updateStatus('停止失败');
    }
  }

  // 处理VAD消息
  private handleVADMessage(data: any) {
    switch (data.type) {
      case 'voiceActivityChanged':
        console.log('[AudioProcessor] 语音活动状态变化:', data.isActive);
        if (this.onVoiceActivityChanged) {
          this.onVoiceActivityChanged(data.isActive);
        }
        break;

      case 'audioAnalysis':
        if (this.onAudioAnalysis) {
          this.onAudioAnalysis(data);
        }
        break;

      default:
        console.log('[AudioProcessor] 未知VAD消息类型:', data.type);
    }
  }

  // 处理音量消息
  private handleVolumeMessage(data: any) {
    if (data.volume !== undefined && this.onVolumeChanged) {
      this.onVolumeChanged(data.volume);
    }
  }

  // 更新VAD参数
  updateVADParameters(params: Partial<typeof this.vadParams>) {
    this.vadParams = { ...this.vadParams, ...params };

    if (this.workletNode) {
      this.workletNode.port.postMessage({
        type: 'updateParams',
        data: this.vadParams
      });
    }
  }

  // 重置处理器
  reset() {
    if (this.workletNode) {
      this.workletNode.port.postMessage({
        type: 'reset'
      });
    }
  }

  // 获取当前状态
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isProcessing: this.isProcessing,
      vadParams: this.vadParams
    };
  }

  // 销毁处理器
  destroy() {
    this.stopProcessing();

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.isInitialized = false;
    this.updateStatus('已销毁');
  }

  // 获取处理后的音频流
  getProcessedStream(): MediaStream | null {
    return this.destinationNode ? this.destinationNode.stream : null;
  }

  // 获取VAD处理器代码
  private getVADWorkletCode(): string {
    return `
class VADProcessor extends AudioWorkletProcessor {
  constructor() {
    super();

    // VAD参数 - 参考live-api-web-console优化
    this.vadThreshold = 0.02;
    this.silenceThreshold = 0.005;
    this.speechFrames = 0;
    this.silenceFrames = 0;
    this.minSpeechFrames = 20;
    this.minSilenceFrames = 60;
    this.maxSilenceFrames = 200;
    this.smoothingFactor = 0.7;

    // 降噪参数
    this.noiseGateThreshold = 0.008;
    this.noiseReduction = 0.3;

    // 状态
    this.isVoiceActive = false;
    this.frameCount = 0;
    this.updateInterval = 10;

    // 平滑音量
    this.smoothedVolume = 0;

    // 语音活动历史缓冲
    this.activityBuffer = [];
    this.bufferSize = 10;

    // 监听参数更新
    this.port.onmessage = (event) => {
      const { type, data } = event.data;
      if (type === 'updateParams') {
        this.updateParameters(data);
      } else if (type === 'reset') {
        this.reset();
      }
    };
  }

  updateParameters(params) {
    Object.assign(this, params);
  }

  reset() {
    this.speechFrames = 0;
    this.silenceFrames = 0;
    this.isVoiceActive = false;
    this.frameCount = 0;
    this.smoothedVolume = 0;
    this.activityBuffer = [];
  }

  // 计算音频能量（RMS）并应用平滑处理
  calculateEnergy(audioData) {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    const rms = Math.sqrt(sum / audioData.length);

    // 应用平滑处理，参考live-api-web-console
    this.smoothedVolume = Math.max(rms, this.smoothedVolume * this.smoothingFactor);

    return this.smoothedVolume;
  }

  // 应用噪声门
  applyNoiseGate(audioData, energy) {
    if (energy < this.noiseGateThreshold) {
      const attenuation = Math.max(0.1, energy / this.noiseGateThreshold * this.noiseReduction);
      for (let i = 0; i < audioData.length; i++) {
        audioData[i] *= attenuation;
      }
    }
  }

  // 改进的VAD检测，参考live-api-web-console的稳健性
  detectVoiceActivity(energy) {
    this.frameCount++;

    // 基于能量阈值的语音检测
    const isCurrentFrameVoice = energy > this.vadThreshold;

    // 更新活动缓冲区
    this.activityBuffer.push(isCurrentFrameVoice ? 1 : 0);
    if (this.activityBuffer.length > this.bufferSize) {
      this.activityBuffer.shift();
    }

    // 计算缓冲区中语音帧的比例
    const voiceRatio = this.activityBuffer.reduce((sum, val) => sum + val, 0) / this.activityBuffer.length;

    // 更稳健的帧计数逻辑
    if (isCurrentFrameVoice) {
      this.speechFrames++;
      this.silenceFrames = Math.max(0, this.silenceFrames - 2); // 语音时快速减少静音计数
    } else {
      this.silenceFrames++;
      this.speechFrames = Math.max(0, this.speechFrames - 1); // 静音时缓慢减少语音计数
    }

    // 状态转换逻辑 - 更保守的判断
    const wasVoiceActive = this.isVoiceActive;

    if (!this.isVoiceActive) {
      // 开始语音：需要连续的语音帧和足够的语音比例
      if (this.speechFrames >= this.minSpeechFrames && voiceRatio > 0.6) {
        this.isVoiceActive = true;
      }
    } else {
      // 结束语音：需要连续的静音帧和较低的语音比例
      if (this.silenceFrames >= this.minSilenceFrames && voiceRatio < 0.3) {
        this.isVoiceActive = false;
        this.speechFrames = 0;
        this.silenceFrames = 0;
      } else if (this.silenceFrames >= this.maxSilenceFrames) {
        // 强制结束：超过最大静音帧数
        this.isVoiceActive = false;
        this.speechFrames = 0;
        this.silenceFrames = 0;
      }
    }

    // 发送状态变化通知
    if (wasVoiceActive !== this.isVoiceActive) {
      this.port.postMessage({
        type: 'voiceActivityChanged',
        isActive: this.isVoiceActive,
        timestamp: this.frameCount,
        voiceRatio: voiceRatio
      });
    }

    return this.isVoiceActive;
  }

  process(inputs, outputs) {
    const input = inputs[0];
    const output = outputs[0];

    if (input.length > 0 && output.length > 0) {
      const inputChannel = input[0];
      const outputChannel = output[0];

      // 计算音频能量
      const energy = this.calculateEnergy(inputChannel);

      // 复制输入到输出
      for (let i = 0; i < inputChannel.length; i++) {
        outputChannel[i] = inputChannel[i];
      }

      // 应用降噪处理
      this.applyNoiseGate(outputChannel, energy);

      // VAD检测
      const isVoiceActive = this.detectVoiceActivity(energy);

      // 定期发送分析数据
      if (this.frameCount % this.updateInterval === 0) {
        // 计算当前的voiceRatio
        const currentVoiceRatio = this.activityBuffer.length > 0 ?
          this.activityBuffer.reduce((sum, val) => sum + val, 0) / this.activityBuffer.length : 0;

        this.port.postMessage({
          type: 'audioAnalysis',
          energy: energy,
          isVoiceActive: isVoiceActive,
          speechFrames: this.speechFrames,
          silenceFrames: this.silenceFrames,
          voiceRatio: currentVoiceRatio,
          timestamp: this.frameCount
        });
      }
    }

    return true;
  }
}
    `;
  }

  // 获取音量计处理器代码 - 参考live-api-web-console
  private getVolumeMeterWorkletCode(): string {
    return `
class VolumeMeter extends AudioWorkletProcessor {
  constructor() {
    super();
    this.volume = 0;
    this.updateIntervalInMS = 25; // 25ms更新间隔
    this.nextUpdateFrame = this.updateIntervalInMS;
    this.smoothingFactor = 0.7; // 平滑因子

    this.port.onmessage = event => {
      if (event.data.updateIntervalInMS) {
        this.updateIntervalInMS = event.data.updateIntervalInMS;
      }
    };
  }

  get intervalInFrames() {
    return (this.updateIntervalInMS / 1000) * sampleRate;
  }

  process(inputs, outputs) {
    const input = inputs[0];
    const output = outputs[0];

    if (input.length > 0 && output.length > 0) {
      const inputChannel = input[0];
      const outputChannel = output[0];

      // 复制输入到输出
      for (let i = 0; i < inputChannel.length; i++) {
        outputChannel[i] = inputChannel[i];
      }

      // 计算RMS音量
      let sum = 0;
      for (let i = 0; i < inputChannel.length; i++) {
        sum += inputChannel[i] * inputChannel[i];
      }
      const rms = Math.sqrt(sum / inputChannel.length);

      // 应用平滑处理，参考live-api-web-console
      this.volume = Math.max(rms, this.volume * this.smoothingFactor);

      this.nextUpdateFrame -= inputChannel.length;
      if (this.nextUpdateFrame < 0) {
        this.nextUpdateFrame += this.intervalInFrames;
        this.port.postMessage({ volume: this.volume });
      }
    }

    return true;
  }
}
    `;
  }

  // 检查浏览器支持
  static isSupported(): boolean {
    return !!(window.AudioContext || (window as any).webkitAudioContext) &&
           !!(window as any).AudioWorkletNode;
  }
}
